import logging
from uuid import UUID

from fastapi import APIRouter, status

from constants.operation_ids import operation_ids
from dependencies import AuthServiceDep, OwnerAndTeamPermissionDep
from schemas import SignalRToken


__all__ = ['router']


logger = logging.getLogger(__name__)

router = APIRouter(prefix='/auth')


@router.post(
    '/signal-r/jwt',
    operation_id=operation_ids.auth.CREATE_SIGNAL_R_JWT,
    status_code=status.HTTP_201_CREATED,
    dependencies=(OwnerAndTeamPermissionDep,),
)
async def create_signal_r_jwt(
    conversation_id: UUID,
    auth_service: AuthServiceDep,
) -> SignalRToken:
    """
    Create a new SignalR JWT.
    """
    return auth_service.generate_signal_r_jwt(conversation_id)
