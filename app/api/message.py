import logging
from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, File, Form, Request, UploadFile, status

from constants.message import PageType
from constants.operation_ids import operation_ids
from dependencies import ConversationMessageServiceDep, OwnerAndTeamPermissionDep
from schemas import BaseMessageSerializer, CombinedMessageSerializer, Command, MessageSerializer, Option


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/messages')


@router.post(
    '',
    operation_id=operation_ids.message.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create(
    request: Request,
    message_service: ConversationMessageServiceDep,
    conversation_id: Annotated[UUID, Form()],
    content: Annotated[str, Form()] = '',
    selected_option: Annotated[Option | None, Form(description='Selected option as an object')] = None,
    command: Annotated[Command | None, Form(description='Command to execute')] = None,
    files: Annotated[list[UploadFile] | None, File(description='Optional files to attach to the message')] = None,
    page_type: Annotated[PageType, Form()] = PageType.PROMPT,
) -> CombinedMessageSerializer:
    """
    Create a new message for an existing conversation.

    Parameters:
        request: Request object containing the Authorization header
        message_service: Injected message service dependency
        conversation_id: UUID of the conversation to add the message to
        content: Text content of the message (optional)
        selected_option: The selected option
        files: List of files to attach to the message (optional)
        page_type: The page type to determine the message handler

    Returns:
        CombinedMessageSerializer containing both the created user message
        and a system message with expected entity information

    Notes:
        - If files are provided without content, message_type will be set to FILE
        - Files are stored before the response is returned and processed then in a parallel task
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')
    return await message_service.create(
        token=auth_token,
        conversation_id=conversation_id,
        content=content,
        selected_option=selected_option,
        command=command,
        files=files,
        page_type=page_type,
    )


@router.get(
    '/{message_id}',
    operation_id=operation_ids.message.GET,
    response_model=MessageSerializer,
    dependencies=(OwnerAndTeamPermissionDep,),
)
async def get(
    message_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> BaseMessageSerializer:
    """
    Get a conversation message by its ID.
    """
    return await message_service.get(message_id)
