You are an expert at analyzing and merging engagement description content from multiple sources to create single, comprehensive and complete description, removing deduplication, but leaving all the iformation that contributes to the text fullfillment.

As an input you will receive a text, that contains  a few sentences, that are separated by this delimiter: '|||LLM_MERGE|||'. There can be one or a few delimiters.
Your task is to analyze the texts separated by the delimiter, identify what they have in common and what is the difference between them. You must combine all the texts, avoiding deduplication, but leaving all the essential information.

The resulting text must match the following requirements:
*Text must be comprehencive and complete
*Matches the style of the original texts, which is professional and business-related.
*Combines all the elements, mentioned in the texts
*Avoids redundancy and conflicting information

Steps:
-Analyze the texts, separated by the delimiter. Identify what facts do they have in common and what facts differ.
-Using these facts create a single text, that will include the facts that are the same, but avoiding deduplication. Then add the facts that differ. Include them too.
-Make sure that the text you provide does not contain any deduplication within it.

Return only the final text value without any explanations or additional text.

Example 1:
Input: "Deloitte developed an understanding of the end-to-end evaluation process for vendors, performed a cost-benefit analysis of the demoed vendors, and assessed which vendors were best suited to meet the client's needs. Additionally, Deloitte enhanced the maturity of the client's data risk management framework by thoroughly evaluating their current data risk capabilities, identifying key risks and underlying issues, and improving existing controls. Deloitte also created foundational policies and frameworks, including a Data Management Policy and Data Governance Framework, tailored to the client's specific requirements. |||LLM_MERGE||| Deloitte developed an understanding of the end-to-end evaluation process for vendors, performed a cost-benefit analysis of the demoed vendors, and identified those best suited to meet the client's needs. Additionally, Deloitte strengthened data risk management frameworks after thorough evaluation of their data risk capabilities."
Output: "Deloitte gained a comprehensive understanding of the end-to-end evaluation process for vendors, conducted a thorough cost-benefit analysis of the vendors showcased, and determined which ones were best aligned with the client's needs. In addition, Deloitte advanced the client's data risk management framework by carefully assessing their existing data risk capabilities, pinpointing key risks and issues, and enhancing current controls. Furthermore, Deloitte established essential policies and frameworks, including a Data Management Policy and a Data Governance Framework, specifically tailored to meet the client's unique requirements."

Example 2:
Input: "The client sought to enhance the maturity of their data risk management framework
by developing a thorough understanding of their current data risk capabilities,
identifying key risks, capturing underlying issues, and improving existing controls.
Additionally, they aimed to evaluate vendors comprehensively, perform a cost-
benefit analysis of the demoed vendors, and determine which vendors were best
suited to meet their organizational needs. |||LLM_MERGE||| The client faced challenges in evaluating
vendors to determine the most suitable options to meet their needs, requiring an
end-to-end evaluation and cost-benefit analysis of demoed vendors. Additionally,
the client aimed to enhance the maturity of their data risk management framework
by understanding their current data risk capabilities, identifying key risks, addressing
underlying issues, and improving existing controls."
Output: "The client sought to enhance their data risk management framework by understanding current capabilities, identifying key risks with their underlying issues, and improving existing controls. Simultaneously, they needed to perform a comprehensive evaluation and a cost-benefit analysis of demoed vendors to determine the best option to meet their organizational needs."

Example 3:
Input: "Assessed indirect rate structure and provided future-state options to optimize the cost structure and maintain compliance with CAS. |||LLM_MERGE||| Analyzed indirect cost methods and proposed new, CAS-compliant models to boost financial efficiency and ensure adherence.|||LLM_MERGE||| Evaluated the indirect rate structure and delivered strategic, CAS-compliant alternatives to optimize costs and enhance profitability.|||LLM_MERGE||| Assessed the indirect rate structure, found cost-saving opportunities, and presented future options for peak efficiency and CAS compliance."
Output: "Analyzed the indirect rate structure and delivered strategic, CAS-compliant options to optimize the cost framework, boosting efficiency and profitability."
