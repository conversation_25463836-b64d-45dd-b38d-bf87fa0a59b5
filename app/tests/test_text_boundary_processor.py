import pytest

from services import TextBoundaryProcessor


class TestTextBoundaryProcessor:
    """Test cases for TextBoundaryProcessor functionality."""

    @pytest.fixture
    def processor(self):
        """Create a TextBoundaryProcessor instance."""
        return TextBoundaryProcessor()

    def test_analyze_selection_boundaries_complete_words(self, processor):
        """Test boundary analysis with complete word selection."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'organization encountered'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is False
        assert result['expanded_snippet'] == snippet
        assert result['reason'] == 'Selection has proper word boundaries'

    def test_analyze_selection_boundaries_starts_mid_word(self, processor):
        """Test boundary analysis when selection starts mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'rganization encountered'  # Starts mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is True
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection starts mid-word'

    def test_analyze_selection_boundaries_ends_mid_word(self, processor):
        """Test boundary analysis when selection ends mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'organization encoun'  # Ends mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is True
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection ends mid-word'

    def test_analyze_selection_boundaries_both_mid_word(self, processor):
        """Test boundary analysis when selection starts and ends mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'rganization encoun'  # Both start and end mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is True
        assert result['ends_mid_word'] is True
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection starts and ends mid-word'

    def test_analyze_selection_boundaries_snippet_not_found(self, processor):
        """Test boundary analysis when snippet is not found."""
        full_text = 'The organization encountered challenges.'
        snippet = 'nonexistent text'
        snippet_index = -1

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is False
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is False
        assert result['expanded_snippet'] == snippet
        assert result['reason'] == 'Snippet not found in full text'

    def test_process_text_for_editing_normal_case(self, processor):
        """Test text processing for normal word boundary case."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'organization encountered'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' challenges during implementation.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_process_text_for_editing_with_expansion(self, processor):
        """Test text processing when boundary expansion is needed."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'rganization encoun'  # Needs expansion

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == 'organization encountered'  # Expanded
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' challenges during implementation.'  # Correct expectation
        assert result['boundary_analysis']['needs_expansion'] is True

    def test_process_text_for_editing_snippet_not_found(self, processor):
        """Test text processing when snippet is not found."""
        full_text = 'The organization encountered challenges.'
        snippet = 'nonexistent text'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == ''
        assert result['text_after_snippet'] == ''
        assert result['boundary_analysis']['is_valid_selection'] is False

    def test_validate_reconstructed_text_no_issues(self, processor):
        """Test text validation when no spacing issues exist."""
        text_before = 'The '
        new_snippet = 'company faced'
        text_after = ' challenges during implementation.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The company faced challenges during implementation.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_validate_reconstructed_text_missing_space_after_punctuation(self, processor):
        """Test text validation fixes missing spaces after punctuation."""
        text_before = 'The system has flaws'
        new_snippet = ',and'
        text_after = ' needs improvement.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The system has flaws, and needs improvement.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_validate_reconstructed_text_multiple_spaces(self, processor):
        """Test text validation fixes multiple consecutive spaces."""
        text_before = 'The '
        new_snippet = 'organization   encountered'
        text_after = ' challenges.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The organization encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_hyphenated_word_handling(self, processor):
        """Test that hyphenated words are handled correctly."""
        full_text = 'The anti-aliasing feature improved performance significantly.'
        snippet = 'anti-aliasing feature'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' improved performance significantly.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_punctuation_boundary_handling(self, processor):
        """Test handling of selections that include punctuation."""
        full_text = 'Data processing, analysis and reporting were completed.'
        snippet = 'processing, analysis'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'Data '
        assert result['text_after_snippet'] == ' and reporting were completed.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_edge_case_start_of_text(self, processor):
        """Test selection at the very start of text."""
        full_text = 'Organization encountered challenges.'
        snippet = 'Organization'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == ''
        assert result['text_after_snippet'] == ' encountered challenges.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_edge_case_end_of_text(self, processor):
        """Test selection at the very end of text."""
        full_text = 'The organization encountered challenges.'
        snippet = 'challenges.'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The organization encountered '
        assert result['text_after_snippet'] == ''
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_single_character_selection(self, processor):
        """Test single character selections."""
        full_text = 'The organization encountered challenges.'
        snippet = 'o'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization'

    def test_sentence_start_detection_beginning_of_text(self, processor):
        """Test sentence start detection at the beginning of text."""
        full_text = 'The organization encountered challenges.'
        snippet = 'he organization'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is True
        assert result['original_first_char_capitalized'] is True
        assert result['expanded_snippet'] == 'The organization'

    def test_sentence_start_detection_after_period(self, processor):
        """Test sentence start detection after period."""
        full_text = 'First sentence. The organization encountered challenges.'
        snippet = 'he organization'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is True
        assert result['original_first_char_capitalized'] is True
        assert result['expanded_snippet'] == 'The organization'

    def test_sentence_start_detection_after_exclamation(self, processor):
        """Test sentence start detection after exclamation mark."""
        full_text = 'Great work! The organization encountered challenges.'
        snippet = 'he organization'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is True
        assert result['original_first_char_capitalized'] is True
        assert result['expanded_snippet'] == 'The organization'

    def test_sentence_start_detection_after_question(self, processor):
        """Test sentence start detection after question mark."""
        full_text = 'What happened? The organization encountered challenges.'
        snippet = 'he organization'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is True
        assert result['original_first_char_capitalized'] is True
        assert result['expanded_snippet'] == 'The organization'

    def test_mid_sentence_detection(self, processor):
        """Test that mid-sentence selections are not marked as sentence start."""
        full_text = 'The organization encountered many challenges during implementation.'
        snippet = 'ncountered many'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is False
        assert result['original_first_char_capitalized'] is False
        assert result['expanded_snippet'] == 'encountered many'

    def test_preserve_sentence_capitalization_sentence_start(self, processor):
        """Test capitalization preservation for sentence-initial words."""
        text_before = ''
        new_snippet = 'the company'
        text_after = ' encountered challenges.'
        boundary_analysis = {'starts_sentence': True, 'original_first_char_capitalized': True}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = 'The company encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_preserve_sentence_capitalization_after_period(self, processor):
        """Test capitalization preservation after period."""
        text_before = 'First sentence. '
        new_snippet = 'the company'
        text_after = ' encountered challenges.'
        boundary_analysis = {'starts_sentence': True, 'original_first_char_capitalized': True}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = 'First sentence. The company encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_preserve_sentence_capitalization_mid_sentence_no_change(self, processor):
        """Test that mid-sentence words don't get capitalized."""
        text_before = 'The company '
        new_snippet = 'successfully handled'
        text_after = ' the challenges.'
        boundary_analysis = {'starts_sentence': False, 'original_first_char_capitalized': False}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = 'The company successfully handled the challenges.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_preserve_sentence_capitalization_already_capitalized(self, processor):
        """Test that already capitalized sentence-initial words remain unchanged."""
        text_before = ''
        new_snippet = 'The company'
        text_after = ' encountered challenges.'
        boundary_analysis = {'starts_sentence': True, 'original_first_char_capitalized': True}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = 'The company encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_preserve_sentence_capitalization_with_spacing_fixes(self, processor):
        """Test capitalization preservation combined with spacing fixes."""
        text_before = 'Previous sentence. '
        new_snippet = 'the company,faced'
        text_after = ' challenges.'
        boundary_analysis = {'starts_sentence': True, 'original_first_char_capitalized': True}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = 'Previous sentence. The company, faced challenges.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_preserve_sentence_capitalization_empty_snippet(self, processor):
        """Test capitalization preservation with empty snippet."""
        text_before = ''
        new_snippet = ''
        text_after = ' encountered challenges.'
        boundary_analysis = {'starts_sentence': True, 'original_first_char_capitalized': True}

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            text_before, new_snippet, text_after, boundary_analysis
        )

        expected = ' encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_preserve_sentence_capitalization_no_boundary_analysis(self, processor):
        """Test that method works without boundary analysis."""
        text_before = ''
        new_snippet = 'the company'
        text_after = ' encountered challenges.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'the company encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_sentence_start_detection_with_whitespace_after_period(self, processor):
        """Test sentence start detection with multiple whitespace characters after period."""
        full_text = 'First sentence.   The organization encountered challenges.'
        snippet = 'he organization'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is True
        assert result['original_first_char_capitalized'] is True
        assert result['expanded_snippet'] == 'The organization'

    def test_sentence_start_detection_abbreviation_period(self, processor):
        """Test that abbreviations with periods don't trigger sentence start incorrectly."""
        full_text = 'The U.S. organization encountered challenges.'
        snippet = 'rganization encountered'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['starts_sentence'] is False
        assert result['original_first_char_capitalized'] is False
        assert result['expanded_snippet'] == 'organization encountered'

    def test_complete_capitalization_preservation_workflow(self, processor):
        """Test the complete workflow from selection to AI rewrite with capitalization preservation."""
        full_text = 'The organization encountered challenges.'
        snippet = 'he organization'

        processed_data = processor.process_text_for_editing(full_text, snippet)

        assert processed_data['boundary_analysis']['starts_sentence'] is True
        assert processed_data['boundary_analysis']['original_first_char_capitalized'] is True
        assert processed_data['snippet'] == 'The organization'

        ai_rewritten_snippet = 'the company'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data['boundary_analysis'],
        )

        expected = 'The company encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is True
        assert corrected_text.startswith('The company')

    def test_trailing_space_extraction(self, processor):
        """Test extraction of trailing spaces from text."""
        assert processor._extract_trailing_spaces('word ') == ' '
        assert processor._extract_trailing_spaces('word  ') == '  '
        assert processor._extract_trailing_spaces('word\t') == '\t'
        assert processor._extract_trailing_spaces('word \t ') == ' \t '
        assert processor._extract_trailing_spaces('word') == ''
        assert processor._extract_trailing_spaces('') == ''
        assert processor._extract_trailing_spaces('   ') == '   '

    def test_trailing_space_preservation_multiple_spaces(self, processor):
        """Test preservation of multiple trailing spaces."""
        full_text = 'The quick brown  fox jumps over the lazy dog'
        snippet = 'brown  '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast  fox jumps over the lazy dog'
        assert corrected_text == expected
        assert was_corrected is True

    def test_trailing_space_preservation_tab_character(self, processor):
        """Test preservation of tab characters in trailing spaces."""
        full_text = 'The quick brown\tfox jumps over the lazy dog'
        snippet = 'brown\t'

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast\tfox jumps over the lazy dog'
        assert corrected_text == expected
        assert was_corrected is True

    def test_trailing_space_preservation_mixed_whitespace(self, processor):
        """Test preservation of mixed whitespace characters."""
        full_text = 'The quick brown \t fox jumps over the lazy dog'
        snippet = 'brown \t '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast \t fox jumps over the lazy dog'
        assert corrected_text == expected
        assert was_corrected is True

    def test_trailing_space_preservation_ai_has_different_spaces(self, processor):
        """Test when AI rewrite has different trailing spaces than original."""
        full_text = 'The quick brown  fox jumps over the lazy dog'
        snippet = 'brown  '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast '

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast  fox jumps over the lazy dog'
        assert corrected_text == expected
        assert was_corrected is True

    def test_trailing_space_preservation_ai_has_same_spaces(self, processor):
        """Test when AI rewrite has same trailing spaces as original."""
        full_text = 'The quick brown  fox jumps over the lazy dog'
        snippet = 'brown  '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast  '

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast  fox jumps over the lazy dog'
        assert corrected_text == expected
        assert was_corrected is False

    def test_trailing_space_preservation_no_original_spaces(self, processor):
        """Test when original selection has no trailing spaces."""
        full_text = 'The quick brown fox jumps over the lazy dog'
        snippet = 'brown'

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'fast '

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The quick fast fox jumps over the lazy dog'
        assert corrected_text == expected

    def test_trailing_space_preservation_with_capitalization(self, processor):
        """Test trailing space preservation combined with capitalization preservation."""
        full_text = 'The organization encountered challenges.'
        snippet = 'he organization '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        ai_rewritten_snippet = 'the company'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data.get('boundary_analysis'),
        )

        expected = 'The company  challenges.'
        assert corrected_text == expected
        assert was_corrected is True
        assert corrected_text.startswith('The company ')

    def test_complete_word_merging_bug_fix_integration(self, processor):
        """Integration test demonstrating the complete fix for the word merging bug."""
        full_text = 'Deloitte implements a new cloud-based data ingestion pipeline to effectively handle vast amounts of patient data'
        snippet = 'new cloud-based '

        processed_data = processor.process_text_for_editing(full_text, snippet)

        assert processed_data['snippet'] == 'new cloud-based '
        assert processed_data['boundary_analysis']['original_trailing_spaces'] == ' '

        ai_rewritten_snippet = 'advanced distributed'

        corrected_text, was_corrected = processor.validate_reconstructed_text(
            processed_data['text_before_snippet'],
            ai_rewritten_snippet,
            processed_data['text_after_snippet'],
            processed_data['boundary_analysis'],
        )

        expected = 'Deloitte implements a advanced distributed data ingestion pipeline to effectively handle vast amounts of patient data'
        assert corrected_text == expected
        assert was_corrected is True
        assert 'advanced distributed data' in corrected_text
        assert 'distributeddata' not in corrected_text

        words = corrected_text.split()
        assert 'advanced' in words
        assert 'distributed' in words
        assert 'data' in words
        for word in words:
            assert 'distributeddata' not in word
            assert 'advanceddistributed' not in word
