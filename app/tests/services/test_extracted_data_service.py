from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from constants.extracted_data import DataSourceType
from repositories.extracted_data import ExtractedDataRepository
from schemas import CombinedExtractedDataResponse, ExtractedData, TeamMemberGraphItem
from services.extracted_data.service import ExtractedDataService


class TestExtractedDataService:
    """Unit tests for the ExtractedDataService class."""

    @pytest.fixture
    def extracted_data_service(self):
        """Create a ExtractedDataService instance with mocked dependencies."""
        mock_industry_data_service = AsyncMock()
        mock_fee_and_currency_data_service = AsyncMock()
        mock_role_data_service = AsyncMock()
        mock_service_data_service = AsyncMock()
        mock_user_info_service = AsyncMock()
        mock_extracted_data_repository = AsyncMock()
        mock_conversation_repository = AsyncMock()
        mock_quals_clients_repository = AsyncMock()
        mock_source_of_work_repository = AsyncMock()
        mock_ldmf_country_service = AsyncMock()
        mock_engagement_location_service = AsyncMock()
        return ExtractedDataService(
            industry_data_service=mock_industry_data_service,
            role_data_service=mock_role_data_service,
            fee_and_currency_service=mock_fee_and_currency_data_service,
            user_info_service=mock_user_info_service,
            extracted_data_repository=mock_extracted_data_repository,
            conversation_repository=mock_conversation_repository,
            quals_clients_repository=mock_quals_clients_repository,
            source_of_work_repository=mock_source_of_work_repository,
            ldmf_country_service=mock_ldmf_country_service,
            service_data_service=mock_service_data_service,
            engagement_location_data_service=mock_engagement_location_service,
        )

    async def test_mask_client_name_in_response_success(self, extracted_data_service: ExtractedDataService):
        """Test successful client name masking."""
        client_name = 'Test Client'
        conversation_id = uuid4()
        response = CombinedExtractedDataResponse(
            title=f'Title with {client_name}',
            business_issues=f'Business issues for {client_name}',
            scope_approach=f'Scope for {client_name}',
            value_delivered=f'Value for {client_name}',
            engagement_summary=f'Summary for {client_name}',
            one_line_description=f'One line for {client_name}',
            conversation_id=conversation_id,
        )

        extracted_data_service.extracted_data_repository.update_masked_data = AsyncMock()

        masked_response = await extracted_data_service._mask_client_name_in_response(response, client_name)

        assert masked_response.title == 'Title with the client'
        assert masked_response.business_issues == 'Business issues for the client'
        assert masked_response.scope_approach == 'Scope for the client'
        assert masked_response.value_delivered == 'Value for the client'
        assert masked_response.engagement_summary == 'Summary for the client'
        assert masked_response.one_line_description == 'One line for the client'
        extracted_data_service.extracted_data_repository.update_masked_data.assert_called_once_with(
            conversation_id, response
        )

    async def test_mask_client_name_in_response_no_client_name(self, extracted_data_service: ExtractedDataService):
        """Test client name masking when client name is not in any fields."""
        client_name = 'Test Client'
        conversation_id = uuid4()
        response = CombinedExtractedDataResponse(
            title='Title without client name',
            business_issues='Business issues',
            conversation_id=conversation_id,
        )

        extracted_data_service.extracted_data_repository.update_masked_data = AsyncMock()

        masked_response = await extracted_data_service._mask_client_name_in_response(response, client_name)

        assert masked_response.title == 'Title without client name'
        assert masked_response.business_issues == 'Business issues'
        extracted_data_service.extracted_data_repository.update_masked_data.assert_called_once_with(
            conversation_id, response
        )

    async def test_mask_client_name_in_response_some_fields_are_none(
        self, extracted_data_service: ExtractedDataService
    ):
        """Test client name masking when some fields are None."""
        client_name = 'Test Client'
        conversation_id = uuid4()
        response = CombinedExtractedDataResponse(
            title=f'Title with {client_name}',
            business_issues=None,
            conversation_id=conversation_id,
        )

        extracted_data_service.extracted_data_repository.update_masked_data = AsyncMock()

        masked_response = await extracted_data_service._mask_client_name_in_response(response, client_name)

        assert masked_response.title == 'Title with the client'
        assert masked_response.business_issues is None
        extracted_data_service.extracted_data_repository.update_masked_data.assert_called_once_with(
            conversation_id, response
        )

    @pytest.mark.asyncio
    async def test_update_masked_data_success(self, db_session, test_conversation_id):
        """Test successful update of masked data using real db_session and repository."""

        # Use a mock for ConversationRepository, but real ExtractedDataRepository
        mock_conversation_repository = AsyncMock()
        mock_conversation_repository.get_internal_id = AsyncMock(return_value=1)

        repository = ExtractedDataRepository(
            db_session=db_session, conversation_repository=mock_conversation_repository
        )

        masked_data = CombinedExtractedDataResponse(
            title='Title with the client',
            business_issues='Business issues for the client',
            scope_approach='Scope for the client',
            value_delivered='Value for the client',
            engagement_summary='Summary for the client',
            one_line_description='One line for the client',
        )

        # Call the real method
        await repository.update_masked_data(test_conversation_id, masked_data)

        # Optionally, verify the DB state here (e.g., query and assert values)
        # For now, just ensure no exceptions and flush was called
        # If you want to check flush, you can mock db_session.flush and assert
        extract_data: ExtractedData | None = await repository.get(test_conversation_id, DataSourceType.PROMPT)
        assert extract_data is not None
        assert extract_data.title == masked_data.title
        assert extract_data.business_issues == masked_data.business_issues
        assert extract_data.engagement_summary == masked_data.engagement_summary
        assert extract_data.one_line_description == masked_data.one_line_description

    @pytest.mark.asyncio
    async def test_enrich_team_roles_handles_all_members(self, extracted_data_service: ExtractedDataService):
        """Test that _enrich_team_roles returns all members, including those without API results."""
        # Mock the external service calls
        extracted_data_service.role_data_service.search_team_members = AsyncMock()
        extracted_data_service.user_info_service.get = AsyncMock()

        # --- Test Data ---
        valid_member_data = {
            'id': 537077,
            'userId': 537077,
            'name': 'Shardanova, Natalia',
            'location': 'United States',
            'level': None,
            'email': '<EMAIL>',
            'firstName': 'Natalia',
            'lastName': 'Shardanova',
            'secondName': None,
            'deloitteId': 'nshardanova',
            'officeCountry': None,
            'officeCity': None,
        }

        # --- Mocks Setup ---
        # Scenario 1: Valid member found in graph and user info
        valid_graph_item = TeamMemberGraphItem(**valid_member_data)

        async def search_team_members_side_effect(email, token):
            if email == '<EMAIL>':
                return [valid_graph_item]
            elif email == '<EMAIL>':
                return []  # Simulate no results for the unknown user
            elif email == '<EMAIL>':
                return []  # Simulate no results for the third user
            return []

        extracted_data_service.role_data_service.search_team_members.side_effect = search_team_members_side_effect
        extracted_data_service.user_info_service.get.return_value = {'avatar': 'test_avatar_url'}

        # --- Input Data ---
        team_roles_with_mixed_results = [
            {
                'name': 'Shardanova, Natalia',
                'email': '<EMAIL>',
                'roles': ['LCSP'],
                'is_approver': True,
                'is_contact': False,
                'duration': 6,
            },
            {
                'name': 'Unknown User',
                'email': '<EMAIL>',
                'roles': ['Team Member'],
                'is_approver': False,
                'is_contact': True,
                'duration': 3,
            },
            {
                'name': 'Testa, Tiziano',
                'email': '<EMAIL>',
                'roles': ['Consultant'],
                'is_approver': False,
                'is_contact': False,
                'duration': 2,
            },
        ]

        # --- Execution ---
        enriched_roles = await extracted_data_service._enrich_team_roles(team_roles_with_mixed_results, 'fake_token')

        # --- Assertions ---
        # All three members should be returned (no filtering)
        assert len(enriched_roles) == 3

        # First member: has API results
        valid_member = enriched_roles[0]
        assert valid_member['name'] == 'Shardanova, Natalia'
        assert valid_member['email'] == '<EMAIL>'
        assert valid_member['deloitteId'] == 'nshardanova'
        assert valid_member['avatar'] == 'test_avatar_url'
        assert valid_member['id'] == 537077
        assert valid_member['userId'] == 537077

        # Second member: no API results, should have default values
        unknown_member = enriched_roles[1]
        assert unknown_member['name'] == 'Unknown User'
        assert unknown_member['email'] == '<EMAIL>'
        assert unknown_member['deloitteId'] == 'unknown'  # Extracted from email
        assert unknown_member['id'] is None  # Default value
        assert unknown_member['userId'] == 0  # Default value for non-real members
        assert 'avatar' not in unknown_member  # No avatar since no deloitteId lookup
        # Name parsing should work for "FirstName LastName" format
        assert unknown_member['firstName'] == 'Unknown'  # Parsed from name
        assert unknown_member['lastName'] == 'User'  # Parsed from name

        # Original fields should be preserved for both members
        assert valid_member['roles'] == ['LCSP']
        assert valid_member['is_approver'] is True
        assert valid_member['is_contact'] is False
        assert valid_member['duration'] == 6

        assert unknown_member['roles'] == ['Team Member']
        assert unknown_member['is_approver'] is False
        assert unknown_member['is_contact'] is True
        assert unknown_member['duration'] == 3

        # Third member: no API results, should have default values with comma-separated name parsing
        comma_member = enriched_roles[2]
        assert comma_member['name'] == 'Testa, Tiziano'
        assert comma_member['email'] == '<EMAIL>'
        assert comma_member['deloitteId'] == 'testa.tiziano'  # Extracted from email
        assert comma_member['id'] is None  # Default value
        assert comma_member['userId'] == 0  # Default value for non-real members
        assert 'avatar' not in comma_member  # No avatar since no deloitteId lookup
        # Name parsing should work for "LastName, FirstName" format
        assert comma_member['firstName'] == 'Tiziano'  # Parsed from name
        assert comma_member['lastName'] == 'Testa'  # Parsed from name

        # Original fields should be preserved for third member
        assert comma_member['roles'] == ['Consultant']
        assert comma_member['is_approver'] is False
        assert comma_member['is_contact'] is False
        assert comma_member['duration'] == 2
