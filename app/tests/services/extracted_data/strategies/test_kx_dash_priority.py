"""Tests for KXDashPriorityStrategy."""

from datetime import datetime, timezone
from uuid import uuid4

import pytest

from constants.extracted_data import DataSourceType
from schemas import ExtractedData
from services.extracted_data.strategies.kx_dash_priority import KXDashPriorityStrategy


@pytest.fixture
def strategy():
    """Fixture to create an instance of the KXDashPriorityStrategy."""
    return KXDashPriorityStrategy()


def create_test_extracted_data(data_source_type: DataSourceType, title: str | None = None, **kwargs):
    """Helper function to create ExtractedData instances with default values."""
    defaults = {
        'ConversationPublicId': uuid4(),
        'DataSourceType': data_source_type,
        'CreatedAt': datetime.now(timezone.utc),
    }
    if title:
        defaults['title'] = title
    defaults.update(kwargs)
    return ExtractedData.model_validate(defaults)


class TestKXDashPriorityStrategy:
    """Test cases for KXDashPriorityStrategy."""

    def test_kx_dash_title_takes_precedence_over_documents(self, strategy):
        """Test that KX_DASH title overrides document title."""
        # Process document data first
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='Document Title')
        strategy.process(document_data)

        # Process KX_DASH data second
        kx_dash_data = create_test_extracted_data(DataSourceType.KX_DASH, title='KX Dash Title')
        strategy.process(kx_dash_data)

        # KX_DASH should win
        assert strategy.get_engagement_title() == 'KX Dash Title'
        assert strategy.get_engagement_title_source() == DataSourceType.KX_DASH

    def test_kx_dash_title_takes_precedence_over_prompt(self, strategy):
        """Test that KX_DASH title overrides prompt title."""
        # Process prompt data first
        prompt_data = create_test_extracted_data(DataSourceType.PROMPT, title='Prompt Title')
        strategy.process(prompt_data)

        # Process KX_DASH data second
        kx_dash_data = create_test_extracted_data(DataSourceType.KX_DASH, title='KX Dash Title')
        strategy.process(kx_dash_data)

        # KX_DASH should win
        assert strategy.get_engagement_title() == 'KX Dash Title'
        assert strategy.get_engagement_title_source() == DataSourceType.KX_DASH

    def test_kx_dash_title_overrides_existing_non_kx_dash_title(self, strategy):
        """Test that KX_DASH title overrides any existing non-KX_DASH title."""
        # Process document data first
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='Document Title')
        strategy.process(document_data)

        # Process prompt data second (should override document)
        prompt_data = create_test_extracted_data(DataSourceType.PROMPT, title='Prompt Title')
        strategy.process(prompt_data)

        # Verify prompt overrode document
        assert strategy.get_engagement_title() == 'Prompt Title'
        assert strategy.get_engagement_title_source() == DataSourceType.PROMPT

        # Process KX_DASH data third (should override prompt)
        kx_dash_data = create_test_extracted_data(DataSourceType.KX_DASH, title='KX Dash Title')
        strategy.process(kx_dash_data)

        # KX_DASH should win
        assert strategy.get_engagement_title() == 'KX Dash Title'
        assert strategy.get_engagement_title_source() == DataSourceType.KX_DASH

    def test_non_kx_dash_sources_follow_normal_priority_when_no_kx_dash(self, strategy):
        """Test that non-KX_DASH sources follow normal priority order when no KX_DASH data exists."""
        # Process document data first
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='Document Title')
        strategy.process(document_data)

        # Process prompt data second (should override document)
        prompt_data = create_test_extracted_data(DataSourceType.PROMPT, title='Prompt Title')
        strategy.process(prompt_data)

        # Prompt should win (latest non-KX_DASH value)
        assert strategy.get_engagement_title() == 'Prompt Title'
        assert strategy.get_engagement_title_source() == DataSourceType.PROMPT

    def test_kx_dash_title_not_overridden_by_later_sources(self, strategy):
        """Test that once KX_DASH title is set, later non-KX_DASH sources cannot override it."""
        # Process KX_DASH data first
        kx_dash_data = create_test_extracted_data(DataSourceType.KX_DASH, title='KX Dash Title')
        strategy.process(kx_dash_data)

        # Process document data second (should not override KX_DASH)
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='Document Title')
        strategy.process(document_data)

        # Process prompt data third (should not override KX_DASH)
        prompt_data = create_test_extracted_data(DataSourceType.PROMPT, title='Prompt Title')
        strategy.process(prompt_data)

        # KX_DASH should still win
        assert strategy.get_engagement_title() == 'KX Dash Title'
        assert strategy.get_engagement_title_source() == DataSourceType.KX_DASH

    def test_empty_kx_dash_title_allows_other_sources(self, strategy):
        """Test that empty/None KX_DASH title allows other sources to be used."""
        # Process KX_DASH data with no title
        kx_dash_data = create_test_extracted_data(DataSourceType.KX_DASH, title=None)
        strategy.process(kx_dash_data)

        # Process document data
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='Document Title')
        strategy.process(document_data)

        # Document should be used since KX_DASH had no title
        assert strategy.get_engagement_title() == 'Document Title'
        assert strategy.get_engagement_title_source() == DataSourceType.DOCUMENTS

    def test_multiple_kx_dash_sources_latest_wins(self, strategy):
        """Test that when multiple KX_DASH sources exist, the latest one wins."""
        # Process first KX_DASH data
        kx_dash_data1 = create_test_extracted_data(DataSourceType.KX_DASH, title='First KX Dash Title')
        strategy.process(kx_dash_data1)

        # Process second KX_DASH data (should override first)
        kx_dash_data2 = create_test_extracted_data(DataSourceType.KX_DASH, title='Second KX Dash Title')
        strategy.process(kx_dash_data2)

        # Second KX_DASH should win
        assert strategy.get_engagement_title() == 'Second KX Dash Title'
        assert strategy.get_engagement_title_source() == DataSourceType.KX_DASH

    def test_no_title_data_returns_none(self, strategy):
        """Test that when no title data is processed, None is returned."""
        # Process data without titles
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title=None)
        strategy.process(document_data)

        # Should return None
        assert strategy.get_engagement_title() is None
        assert strategy.get_engagement_title_source() is None

    def test_empty_string_title_is_ignored(self, strategy):
        """Test that empty string titles are ignored."""
        # Process data with empty string title
        document_data = create_test_extracted_data(DataSourceType.DOCUMENTS, title='')
        strategy.process(document_data)

        # Should return None (empty string is ignored)
        assert strategy.get_engagement_title() is None
        assert strategy.get_engagement_title_source() is None

        # Process valid title after empty string
        prompt_data = create_test_extracted_data(DataSourceType.PROMPT, title='Valid Title')
        strategy.process(prompt_data)

        # Should use the valid title
        assert strategy.get_engagement_title() == 'Valid Title'
        assert strategy.get_engagement_title_source() == DataSourceType.PROMPT
