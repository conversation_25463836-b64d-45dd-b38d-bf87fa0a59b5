import json
from unittest.mock import AsyncMock, patch

from fastapi import status
import pytest

from constants.engagement import EngagementMessageIntention, engagement_templates
from constants.extracted_data import DataSourceType
from constants.message import MessageRole, MessageType, PageType
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from repositories.field import FieldRepository
from repositories.openai import OpenAIRepository
from schemas import (
    AggregatedData,
    EngagementFieldModificationResponse,
    EngagementMessageIntentClassifierServiceResponse,
    ExtractedData,
)
from services import EngagementFieldModificationService


class TestEngagementFieldModificationIntegration:
    """Integration tests for the complete engagement field modification flow."""

    async def test_business_issues_field_modification_complete_flow(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test complete flow for business issues field modification."""
        # Setup: Create initial extracted data
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.business_issues = 'Original business issues content'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Mock OpenAI responses
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )
        mock_field_content = 'Updated business issues content based on user request'

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please make the business issues section more concise and focused',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
                new_callable=AsyncMock,
                return_value=mock_field_content,
            ),
        ):
            response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        # Check user message
        assert 'user' in response_data
        user_data = response_data['user']
        assert user_data['content'] == message_data['content']
        assert user_data['role'] == MessageRole.USER
        assert user_data['type'] == MessageType.TEXT

        # Check system message
        assert 'system' in response_data
        system_data = response_data['system']
        assert system_data['content'] == engagement_templates.general.edited_text_field_reply

        # Verify data persistence: Check that the field was updated in the database
        updated_data = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert updated_data is not None
        assert updated_data.business_issues == mock_field_content

    async def test_engagement_title_field_modification_complete_flow(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test complete flow for engagement title field modification."""
        # Setup: Create initial extracted data
        extracted_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.title = 'Original engagement title'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Mock OpenAI responses
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )
        mock_field_content = 'Digital Transformation Initiative for Retail Client'

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Change the title to be more specific about digital transformation',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
                new_callable=AsyncMock,
                return_value=mock_field_content,
            ),
        ):
            response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert response.status_code == status.HTTP_201_CREATED, response.json()
        response_data = response.json()

        # Check system message contains success confirmation
        system_data = response_data['system']
        assert system_data['content'] == engagement_templates.general.edited_text_field_reply

        # Verify data persistence
        updated_data: ExtractedData = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert updated_data is not None
        assert updated_data.title == extracted_data.title

    async def test_multiple_engagement_fields_modification(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test modification of multiple engagement fields in sequence."""
        # Setup: Create initial extracted data with multiple fields
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.scope_approach = 'Original scope approach'
        extracted_data.value_delivered = 'Original value delivered'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Test 1: Modify scope_approach
        mock_intent_response_1 = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.SCOPE_APPROACH
        )
        mock_field_content_1 = 'Updated scope approach with detailed methodology'

        message_data_1 = {
            'conversation_id': str(test_conversation_id),
            'content': 'Expand the scope approach section with more details',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response_1,
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
                new_callable=AsyncMock,
                return_value=mock_field_content_1,
            ),
        ):
            response_1 = await async_client.post(message_url, headers=auth_header, data=message_data_1)

        assert response_1.status_code == status.HTTP_201_CREATED
        assert response_1.json()['system']['content'] == engagement_templates.general.edited_text_field_reply

        # Test 2: Modify value_delivered
        mock_intent_response_2 = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.VALUE_DELIVERED_IMPACT
        )
        mock_field_content_2 = 'Enhanced value delivered with quantified impact metrics'

        message_data_2 = {
            'conversation_id': str(test_conversation_id),
            'content': 'Add specific metrics to the value delivered section',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response_2,
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
                new_callable=AsyncMock,
                return_value=mock_field_content_2,
            ),
        ):
            response_2 = await async_client.post(message_url, headers=auth_header, data=message_data_2)

        assert response_2.status_code == status.HTTP_201_CREATED
        assert response_2.json()['system']['content'] == engagement_templates.general.edited_text_field_reply

        # Verify both fields were updated
        final_data = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert final_data is not None
        assert final_data.scope_approach == mock_field_content_1
        assert final_data.value_delivered == mock_field_content_2

    async def test_openai_api_failure_handling(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test handling of OpenAI API failures during field modification."""
        # Mock OpenAI responses with failure
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.ENGAGEMENT_SUMMARY
        )

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please improve the engagement summary',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'services.intent_classifier.IntentClassifierService.classify_intent',
                new_callable=AsyncMock,
                return_value=mock_intent_response,
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
                new_callable=AsyncMock,
                return_value=None,  # Simulate API failure
            ),
        ):
            response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify error handling
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        system_data = response_data['system']
        assert '❌ Failed to update the field' in system_data['content']
        assert 'Failed to generate updated content' in system_data['content']

    async def test_non_field_modification_intent_fallback(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """Test that non-field-modification intents fall back to existing processor."""
        # Mock non-field-modification intent
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.NAVIGATE_TO_RESOURCE_PAGE
        )

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Take me to the resource page',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with patch(
            'services.intent_classifier.IntentClassifierService.classify_intent',
            new_callable=AsyncMock,
            return_value=mock_intent_response,
        ):
            response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify fallback to existing processor
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        system_data = response_data['system']
        # Should contain the existing processor response format
        assert system_data['content'] == engagement_templates.general.kx_prompt_reply


class TestEngagementFieldModificationService:
    """Unit tests for the EngagementFieldModificationService."""

    @pytest.fixture
    def field_repository_mock(self):
        """Create a mock field repository."""
        return AsyncMock(spec=FieldRepository)

    async def test_field_modification_with_existing_data(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
        field_repository_mock,
    ):
        """Test field modification when existing data is present."""

        # Setup: Create existing data
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.business_issues = 'Existing business issues content'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Test field modification
        with patch(
            'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
            new_callable=AsyncMock,
            return_value='Updated business issues content',
        ):
            service = EngagementFieldModificationService(
                extracted_data_service=extracted_data_service_real,
                extracted_data_repository=extracted_data_repository_real_with_autocommit,
                openai_repository=AsyncMock(spec=OpenAIRepository),  # Keep a mock for openai_repository
                field_repository=field_repository_mock,
            )
            result = await service.modify_field(
                conversation_id=test_conversation_id,
                intent=EngagementMessageIntention.BUSINESS_ISSUES,
                user_request='Make it more concise',
            )

        # Verify result
        assert result.success is True
        assert result.field_name == 'business_issues'
        assert result.updated_content == 'Updated business issues content'
        assert result.original_content == 'Existing business issues content'
        assert result.error is None

        # Verify data was saved
        updated_data = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert updated_data is not None
        assert updated_data.business_issues == 'Updated business issues content'

    async def test_field_modification_with_empty_original_content(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
        field_repository_mock,
    ):
        """Test field modification when no existing data is present."""

        # Test field modification with no existing data
        new_business_issues_content = 'New business issues content'
        with patch(
            'services.engagement_field_modification.EngagementFieldModificationService._generate_updated_field_content',
            new_callable=AsyncMock,
            return_value=new_business_issues_content,
        ):
            service = EngagementFieldModificationService(
                extracted_data_service=extracted_data_service_real,
                extracted_data_repository=extracted_data_repository_real_with_autocommit,
                openai_repository=AsyncMock(spec=OpenAIRepository),  # Keep a mock for openai_repository
                field_repository=field_repository_mock,
            )
            result = await service.modify_field(
                conversation_id=test_conversation_id,
                intent=EngagementMessageIntention.BUSINESS_ISSUES,
                user_request='Create a compelling business issues section',
            )

        # Verify result
        assert result.success is True
        assert result.field_name == 'business_issues'
        assert result.updated_content == new_business_issues_content
        assert result.original_content == ''  # Empty original content
        assert result.error is None

        # Verify data was saved
        updated_data = await extracted_data_repository_real_with_autocommit.get(
            test_conversation_id, DataSourceType.PROMPT
        )
        assert updated_data is not None
        assert updated_data.business_issues == new_business_issues_content

    async def test_unsupported_intent_raises_error(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
        field_repository_mock,
    ):
        """Test that unsupported intents raise ValueError."""

        # Create service with mocked OpenAI repository
        mock_openai_repo = AsyncMock(spec=OpenAIRepository)

        service = EngagementFieldModificationService(
            extracted_data_service=extracted_data_service_real,
            extracted_data_repository=extracted_data_repository_real_with_autocommit,
            openai_repository=mock_openai_repo,
            field_repository=field_repository_mock,
        )

        # Test with unsupported intent
        with pytest.raises(ValueError, match='Intent .* is not supported for field modification'):
            await service.modify_field(
                conversation_id=test_conversation_id,
                intent=EngagementMessageIntention.NAVIGATE_TO_RESOURCE_PAGE,
                user_request='Navigate somewhere',
            )
        # Verify that the field repository's create method was not called
        field_repository_mock.create.assert_not_called()


class TestEngagementFieldAggregation:
    """Test that engagement_title field is properly aggregated."""

    async def test_engagement_title_aggregation_from_multiple_sources(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test that engagement_title prioritizes KX_DASH data over other sources."""
        # Create data from different sources
        kx_dash_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        kx_dash_data.title = 'KX Dash Title'
        await extracted_data_repository_real_with_autocommit.update(kx_dash_data)

        prompt_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        prompt_data.title = 'Prompt Title'
        await extracted_data_repository_real_with_autocommit.update(prompt_data)

        # Test aggregation
        aggregated_data: AggregatedData = await extracted_data_service_real.aggregate_data(test_conversation_id)

        # Verify KX_DASH title takes precedence (no concatenation)
        assert aggregated_data.engagement_title is not None
        assert aggregated_data.engagement_title == 'KX Dash Title'
        # Ensure prompt title is not included when KX_DASH title exists
        assert 'Prompt Title' not in aggregated_data.engagement_title

    async def test_engagement_title_kx_dash_priority_over_documents_bug_fix(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test the specific bug fix: KX_DASH title should override document title, not merge."""
        # Create KX_DASH data (simulating existing Dash task with title)
        kx_dash_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        kx_dash_data.title = 'Engagement for A.M.A., Agrupacion Mutual Aseguradora'
        await extracted_data_repository_real_with_autocommit.update(kx_dash_data)

        # Create document data (simulating uploaded document with title)
        document_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        document_data.title = 'Cyber-security Enhancement for'
        await extracted_data_repository_real_with_autocommit.update(document_data)

        # Test aggregation
        aggregated_data: AggregatedData = await extracted_data_service_real.aggregate_data(test_conversation_id)

        # Verify only KX_DASH title is used (bug fix)
        assert aggregated_data.engagement_title is not None
        assert aggregated_data.engagement_title == 'Engagement for A.M.A., Agrupacion Mutual Aseguradora'
        # Ensure no concatenation occurred (this was the bug)
        assert '|' not in aggregated_data.engagement_title
        assert 'Cyber-security Enhancement for' not in aggregated_data.engagement_title

    async def test_engagement_title_fallback_when_no_kx_dash_title(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test that document/prompt titles are used when KX_DASH has no title."""
        # Create KX_DASH data without title
        kx_dash_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        # No title set for KX_DASH
        await extracted_data_repository_real_with_autocommit.update(kx_dash_data)

        # Create document data with title
        document_data: ExtractedData = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        document_data.title = 'Document Title'
        await extracted_data_repository_real_with_autocommit.update(document_data)

        # Test aggregation
        aggregated_data: AggregatedData = await extracted_data_service_real.aggregate_data(test_conversation_id)

        # Verify document title is used when KX_DASH has no title
        assert aggregated_data.engagement_title is not None
        assert aggregated_data.engagement_title == 'Document Title'


class TestEngagementDescriptionCommandIntegrationWithFieldModification:
    """Test integration between command processing and field modification."""

    async def test_command_processing_vs_field_modification_coexistence(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test that command processing and field modification can coexist for the same field."""
        # First, use field modification to update business_issues
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.business_issues = 'Original business issues content'
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Mock field modification intent classification and content generation
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )
        mock_field_content = 'Updated business issues via field modification'

        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Step 1: Use field modification
        message_data_1 = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please update the business issues section',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with patch(
            'repositories.openai.OpenAIRepository.generate_chat_completion',
            side_effect=[mock_intent_response, mock_field_content],
        ):
            response_1 = await async_client.post(message_url, headers=auth_header, data=message_data_1)

        assert response_1.status_code == status.HTTP_201_CREATED

        # Step 2: Use command processing for the same field
        command_data = {
            'command': 'expand',
            'field_name': 'business_issues',
            'context': 'Business issues content to be expanded through command processing',
            'formatted_context': 'Business issues content to be expanded through command processing',
            'snippet': 'content to be expanded',
        }

        # The TextEditService should return only the edited snippet, not the full context
        expected_edited_snippet = 'EXPANDED content to be expanded with additional detail'
        expected_reconstructed_context = (
            'Business issues EXPANDED content to be expanded with additional detail through command processing'
        )

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            message_data_2 = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response_2 = await async_client.post(message_url, headers=auth_header, data=message_data_2)

        assert response_2.status_code == status.HTTP_201_CREATED

        # Verify both approaches work independently
        data_1 = response_1.json()
        data_2 = response_2.json()

        # Field modification response
        assert (
            'business_issues' in data_1['system']['content']
            or engagement_templates.general.edited_text_field_reply in data_1['system']['content']
        )

        # Command processing response
        assert data_2['system']['type'] == str(MessageType.TEXT)
        # Command is in user message, processed result is in system fields
        assert (
            data_2['user']['command']['context'] == 'Business issues content to be expanded through command processing'
        )
        system_fields = data_2['system']['qual_fields']
        assert system_fields['business_issues']['context'] == expected_reconstructed_context

    async def test_field_modification_and_command_affect_different_data_stores(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test that field modification updates extracted data while commands create engagement descriptions."""
        # Setup initial extracted data
        initial_extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        initial_extracted_data.business_issues = 'Initial business issues for field modification'
        await extracted_data_repository_real_with_autocommit.update(initial_extracted_data)

        message_url = url_resolver.reverse(operation_ids.message.CREATE)

        # Step 1: Field modification should update the extracted data
        mock_intent_response = EngagementMessageIntentClassifierServiceResponse(
            intention=EngagementMessageIntention.BUSINESS_ISSUES
        )
        updated_field_content = 'Modified business issues content via field modification'

        message_data_1 = {
            'conversation_id': str(test_conversation_id),
            'content': 'Please revise the business issues',
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        with (
            patch(
                'repositories.openai.OpenAIRepository.generate_chat_completion',
                side_effect=[mock_intent_response, updated_field_content],
            ),
            patch(
                'services.engagement_field_modification.EngagementFieldModificationService.modify_field',
                return_value=EngagementFieldModificationResponse(
                    success=True,
                    field_name='business_issues',
                    updated_content=updated_field_content,
                    original_content='Initial business issues for field modification',
                    error=None,
                ),
            ),
        ):
            response_1 = await async_client.post(message_url, headers=auth_header, data=message_data_1)

        assert response_1.status_code == status.HTTP_201_CREATED

        # Verify extracted data was updated (mock the update since we're mocking the service)
        # The field modification service should have been called and successful response returned
        response_data = response_1.json()
        assert response_data['system']['content']  # System should have responded
        assert response_data['system']['system_reply_type'] == 'engagement_details_field_modified'

        # Step 2: Command processing should create engagement description (not modify extracted data)
        command_data = {
            'command': 'expand',
            'field_name': 'business_issues',
            'context': 'Separate business issues context for command processing',
            'formatted_context': 'Separate business issues context for command processing',
            'snippet': 'context for command',
        }

        # The TextEditService should return only the edited snippet, not the full context
        expected_edited_snippet = 'EXPANDED context for command'
        expected_reconstructed_context = 'Separate business issues EXPANDED context for command processing'

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            message_data_2 = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response_2 = await async_client.post(message_url, headers=auth_header, data=message_data_2)

        assert response_2.status_code == status.HTTP_201_CREATED

        # Verify command response is correct type
        response_data_2 = response_2.json()
        assert response_data_2['system']['type'] == 'text'
        assert response_data_2['system']['system_reply_type'] == 'field_saved'
        assert 'command' in response_data_2['user']  # Command is in user message

        # Verify command processing worked
        data_2 = response_2.json()
        assert data_2['user']['command']['context'] == 'Separate business issues context for command processing'
        # The processed result is in the system qual_fields
        system_fields = data_2['system']['qual_fields']
        assert system_fields['business_issues']['context'] == expected_reconstructed_context
