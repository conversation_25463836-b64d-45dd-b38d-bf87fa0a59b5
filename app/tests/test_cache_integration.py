from unittest.mock import AsyncMock

import pytest

from core.http_client import CustomAsync<PERSON><PERSON>
from repositories import (
    BlobStorageRepository,
    InMemoryCacheRepository,
    LDMFCountriesRepository,
    OpenAIRepository,
    QualsClientsRepository,
)
from schemas import CountryData
from schemas.quals_clients import ClientAPIParamsItem
from services.ldmf_country import LDMFCountryService


class TestLDMFCountryServiceCacheIntegration:
    """Integration tests for LDMFCountryService with centralized cache."""

    @pytest.fixture
    def cache_repository(self) -> InMemoryCacheRepository:
        """Create a cache repository for testing."""
        return InMemoryCacheRepository(
            maxsize=1,
            ttl=3600,
            key_prefix='ldmf_countries:',
        )

    @pytest.fixture
    def mock_ldmf_countries_repository(self) -> LDMFCountriesRepository:
        """Create a mock LDMF countries repository."""
        mock_repo = AsyncMock(spec=LDMFCountriesRepository)
        return mock_repo

    @pytest.fixture
    def mock_countries_blob_repository(self) -> BlobStorageRepository:
        """Create a mock countries blob repository."""
        mock_repo = AsyncMock(spec=BlobStorageRepository)
        # No explicit assignment here, relying on @patch in tests
        return mock_repo

    @pytest.fixture
    def mock_openai_repository(self) -> OpenAIRepository:
        """Create a mock OpenAI repository."""
        return AsyncMock(spec=OpenAIRepository)

    @pytest.fixture
    def ldmf_service(
        self,
        mock_ldmf_countries_repository: LDMFCountriesRepository,
        mock_countries_blob_repository: BlobStorageRepository,
        cache_repository: InMemoryCacheRepository,
        mock_openai_repository: OpenAIRepository,
    ) -> LDMFCountryService:
        """Create LDMFCountryService with mocked dependencies."""
        return LDMFCountryService(
            ldmf_countries_repository=mock_ldmf_countries_repository,
            blob_repository=mock_countries_blob_repository,
            cache_repository=cache_repository,
            openai_service=mock_openai_repository,
        )

    @pytest.fixture
    def sample_countries(self) -> list[CountryData]:
        """Sample country data for testing."""
        return [
            CountryData(id=1, name='United States', memberFirmId=123),
            CountryData(id=2, name='Germany', memberFirmId=456),
            CountryData(id=3, name='Japan', memberFirmId=789),
        ]

    @pytest.mark.disable_autouse
    async def test_cache_miss_and_set(
        self,
        ldmf_service: LDMFCountryService,
        mock_ldmf_countries_repository: LDMFCountriesRepository,
        mock_countries_blob_repository: BlobStorageRepository,
        cache_repository: InMemoryCacheRepository,
        sample_countries: list[CountryData],
    ):
        """Test that cache miss triggers API call and caches result."""
        # Setup mock to return sample countries
        mock_ldmf_countries_repository.list = AsyncMock(return_value=sample_countries)
        mock_countries_blob_repository.upload = AsyncMock()  # Set the mock directly on the instance

        # Verify cache is empty
        cached_data = await cache_repository.get('ldmf_countries')
        assert cached_data is None

        # Call service method
        result = await ldmf_service.list('test_token')

        # Verify API was called
        mock_ldmf_countries_repository.list.assert_called_once_with('test_token')
        mock_countries_blob_repository.upload.assert_awaited()  # Assert on the instance's mock

        # Verify result format
        expected_result = {country.name: country for country in sample_countries}
        assert result == expected_result

        # Verify data was cached (note: serialization may convert objects to dicts)
        cached_data = await cache_repository.get('ldmf_countries')
        assert cached_data is not None
        assert len(cached_data) == len(expected_result)
        # Check that all keys are present
        for key in expected_result.keys():
            assert key in cached_data

    @pytest.mark.disable_autouse
    async def test_cache_hit(
        self,
        ldmf_service: LDMFCountryService,
        mock_ldmf_countries_repository: LDMFCountriesRepository,
        mock_countries_blob_repository: BlobStorageRepository,
        cache_repository: InMemoryCacheRepository,
        sample_countries: list[CountryData],
    ):
        """Test that cache hit returns cached data without API call."""
        # Pre-populate cache
        expected_result = {country.name: country for country in sample_countries}
        await cache_repository.set('ldmf_countries', expected_result)

        # Ensure the list method is an AsyncMock so we can assert calls
        mock_ldmf_countries_repository.list = AsyncMock()
        mock_countries_blob_repository.upload = AsyncMock()  # Set the mock directly on the instance

        # Call service method
        result = await ldmf_service.list('test_token')

        # Verify API was NOT called
        mock_ldmf_countries_repository.list.assert_not_called()
        mock_countries_blob_repository.upload.assert_not_called()  # Assert on the instance's mock

        # Verify cached result was returned (check structure, not exact equality due to serialization)
        assert len(result) == len(expected_result)
        for key in expected_result.keys():
            assert key in result

    async def test_cache_key_consistency(
        self,
        ldmf_service: LDMFCountryService,
        cache_repository: InMemoryCacheRepository,
    ):
        """Test that the service uses the correct cache key."""
        # The service should use the same cache key consistently
        assert ldmf_service._CACHE_KEY == 'ldmf_countries'

        # Verify the cache key is used correctly
        test_data = {'test': 'data'}
        await cache_repository.set(ldmf_service._CACHE_KEY, test_data)

        cached_data = await cache_repository.get(ldmf_service._CACHE_KEY)
        assert cached_data == test_data

    @pytest.mark.disable_autouse
    async def test_error_handling_with_cache(
        self,
        ldmf_service: LDMFCountryService,
        mock_ldmf_countries_repository: LDMFCountriesRepository,
        cache_repository: InMemoryCacheRepository,
    ):
        """Test error handling when API call fails."""
        # Setup mock to raise exception
        mock_ldmf_countries_repository.list = AsyncMock(side_effect=Exception('API Error'))

        # Verify cache is empty
        cached_data = await cache_repository.get('ldmf_countries')
        assert cached_data is None

        # Call service method and expect exception
        with pytest.raises(Exception, match='API Error'):
            await ldmf_service.list('test_token')

        # Verify cache remains empty after error
        cached_data = await cache_repository.get('ldmf_countries')
        assert cached_data is None


class TestQualsClientsRepositoryCacheIntegration:
    """Integration tests for QualsClientsRepository with centralized cache."""

    @pytest.fixture
    def cache_repository(self) -> InMemoryCacheRepository:
        """Create a cache repository for testing."""
        return InMemoryCacheRepository(
            maxsize=2,
            ttl=3600,
            key_prefix='quals_clients:',
        )

    @pytest.fixture
    def mock_http_client(self) -> CustomAsyncClient:
        """Create a mock HTTP client."""
        mock_client = AsyncMock(spec=CustomAsyncClient)
        return mock_client

    @pytest.fixture
    def quals_clients_repo(
        self,
        mock_http_client: CustomAsyncClient,
        cache_repository: InMemoryCacheRepository,
    ) -> QualsClientsRepository:
        """Create QualsClientsRepository with mocked dependencies."""
        return QualsClientsRepository(
            http_client=mock_http_client,
            cache_repository=cache_repository,
        )

    @pytest.fixture
    def sample_references(self) -> list[ClientAPIParamsItem]:
        """Sample reference data for testing."""
        return [
            ClientAPIParamsItem(id=1, name='Yes', catalogue_number=7, catalogue_type=7, sort_order=1),
            ClientAPIParamsItem(id=2, name='No', catalogue_number=7, catalogue_type=7, sort_order=2),
        ]

    @pytest.fixture
    def sample_client_sharing(self) -> list[ClientAPIParamsItem]:
        """Sample client sharing data for testing."""
        return [
            ClientAPIParamsItem(
                id=1,
                name='Client name may be shared internally',
                catalogue_number=69,
                catalogue_type=69,
                sort_order=0,
            ),
            ClientAPIParamsItem(
                id=2,
                name='Client name may NOT be shared internally',
                catalogue_number=69,
                catalogue_type=69,
                sort_order=0,
            ),
        ]

    async def test_get_references_list_cache_miss(
        self,
        quals_clients_repo: QualsClientsRepository,
        mock_http_client: AsyncMock,
        cache_repository: InMemoryCacheRepository,
        sample_references: list[ClientAPIParamsItem],
    ):
        """Test references list cache miss triggers API call."""
        # Verify cache is empty
        cached_data = await cache_repository.get('references_items')
        assert cached_data is None

        # Configure mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = [
            {'id': 1, 'name': 'Yes', 'catalogue_number': 7, 'catalogue_type': 7, 'sort_order': 1},
            {'id': 2, 'name': 'No', 'catalogue_number': 7, 'catalogue_type': 7, 'sort_order': 2},
        ]
        mock_http_client.request.return_value = mock_response

        # Call repository method
        result = await quals_clients_repo.get_references_list('test_token')

        # Verify API was called
        mock_http_client.request.assert_called_once()

        # Verify result is returned
        assert result == sample_references

        # Verify data was cached
        cached_data = await cache_repository.get('references_items')
        assert cached_data == sample_references

    async def test_get_references_list_cache_hit(
        self,
        quals_clients_repo: QualsClientsRepository,
        mock_http_client: AsyncMock,
        cache_repository: InMemoryCacheRepository,
        sample_references: list[ClientAPIParamsItem],
    ):
        """Test references list cache hit returns cached data."""
        # Pre-populate cache
        await cache_repository.set('references_items', sample_references)

        # Call repository method
        result = await quals_clients_repo.get_references_list('test_token')

        # Verify API was NOT called
        mock_http_client.request.assert_not_called()

        # Verify cached result was returned
        assert result == sample_references

    async def test_get_client_sharing_list_cache_miss(
        self,
        quals_clients_repo: QualsClientsRepository,
        mock_http_client: AsyncMock,
        cache_repository: InMemoryCacheRepository,
        sample_client_sharing: list[ClientAPIParamsItem],
    ):
        """Test client sharing list cache miss triggers API call."""
        # Verify cache is empty
        cached_data = await cache_repository.get('name_sharing')
        assert cached_data is None

        # Configure mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = [
            {
                'id': 1,
                'name': 'Client name may be shared internally',
                'catalogue_number': 69,
                'catalogue_type': 69,
                'sort_order': 0,
            },
            {
                'id': 2,
                'name': 'Client name may NOT be shared internally',
                'catalogue_number': 69,
                'catalogue_type': 69,
                'sort_order': 0,
            },
        ]
        mock_http_client.request.return_value = mock_response

        # Call repository method
        result = await quals_clients_repo.get_client_sharing_list('test_token')

        # Verify API was called
        mock_http_client.request.assert_called_once()

        # Verify result is returned
        assert result == sample_client_sharing

        # Verify data was cached
        cached_data = await cache_repository.get('name_sharing')
        assert cached_data == sample_client_sharing

    async def test_get_client_sharing_list_cache_hit(
        self,
        quals_clients_repo: QualsClientsRepository,
        mock_http_client: AsyncMock,
        cache_repository: InMemoryCacheRepository,
        sample_client_sharing: list[ClientAPIParamsItem],
    ):
        """Test client sharing list cache hit returns cached data."""
        # Pre-populate cache
        await cache_repository.set('name_sharing', sample_client_sharing)

        # Call repository method
        result = await quals_clients_repo.get_client_sharing_list('test_token')

        # Verify API was NOT called
        mock_http_client.request.assert_not_called()

        # Verify cached result was returned
        assert result == sample_client_sharing

    async def test_multiple_cache_keys(
        self,
        quals_clients_repo: QualsClientsRepository,
        mock_http_client: AsyncMock,
        cache_repository: InMemoryCacheRepository,
        sample_references: list[ClientAPIParamsItem],
        sample_client_sharing: list[ClientAPIParamsItem],
    ):
        """Test that different methods use different cache keys."""
        # Pre-populate both caches
        await cache_repository.set('references_items', sample_references)
        await cache_repository.set('name_sharing', sample_client_sharing)

        # Call both methods
        references_result = await quals_clients_repo.get_references_list('test_token')
        sharing_result = await quals_clients_repo.get_client_sharing_list('test_token')

        # Verify API was NOT called
        mock_http_client.request.assert_not_called()

        # Verify correct data was returned from cache
        assert references_result == sample_references
        assert sharing_result == sample_client_sharing
