import logging

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['QualRepository']

logger = logging.getLogger(__name__)


class QualRepository:
    """Repository for qual informations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Qual Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = url_join(str(settings.quals_clients_api.base_url), 'quals')

    async def get_team_members_emails(self, qual_id: int, token: str) -> set[str]:
        url = url_join(self._base_path, str(qual_id))
        headers = {'Authorization': f'Bearer {token}'} if token else {}
        try:
            qual_info = (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error getting qual info: %s', e)
            raise e

        team_members = qual_info.get('team', {}).get('teamMembers', [])
        return {team_member.get('email', '') for team_member in team_members}
