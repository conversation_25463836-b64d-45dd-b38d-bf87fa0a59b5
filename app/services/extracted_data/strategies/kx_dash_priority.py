"""KX Dash priority strategy for fields that should prioritize KX_DASH data over other sources."""

from constants.extracted_data import DataSourceType
from schemas import ExtractedData

from .base import BaseAggregationStrategy


__all__ = ['KXDashPriorityStrategy']


class KXDashPriorityStrategy(BaseAggregationStrategy):
    """Strategy that prioritizes KX_DASH data over other sources for specific fields.

    This strategy ensures that when KX_DASH has a value for a field, it takes precedence
    over values from DOCUMENTS or PROMPT sources. If KX_DASH doesn't have a value,
    then the latest non-KX_DASH value is used (following normal priority order).
    """

    def __init__(self):
        # Store both the value and its source type for priority decisions
        self._engagement_title: str | None = None
        self._engagement_title_source: DataSourceType | None = None

    def process(self, extracted_data: ExtractedData) -> None:
        """Process extracted data with KX_DASH priority logic."""
        source_type = extracted_data.data_source_type

        # Handle engagement_title with KX_DASH priority
        if extracted_data.title:
            self._process_engagement_title(extracted_data.title, source_type)

    def _process_engagement_title(self, title: str, source_type: DataSourceType) -> None:
        """Process engagement title with KX_DASH priority logic.

        Priority rules:
        1. If KX_DASH has a title, it always wins
        2. If no KX_DASH title exists, use the latest non-KX_DASH title
        3. If current source is KX_DASH, it overrides any existing value
        """
        if source_type == DataSourceType.KX_DASH:
            # KX_DASH always takes precedence
            self._engagement_title = title
            self._engagement_title_source = source_type
        elif self._engagement_title_source != DataSourceType.KX_DASH:
            # Only update if we don't already have a KX_DASH value
            self._engagement_title = title
            self._engagement_title_source = source_type

    def get_engagement_title(self) -> str | None:
        """Get the engagement title with KX_DASH priority applied."""
        return self._engagement_title

    def get_engagement_title_source(self) -> DataSourceType | None:
        """Get the source type of the current engagement title (for debugging/testing)."""
        return self._engagement_title_source
