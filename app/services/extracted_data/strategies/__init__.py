from .base import BaseAggregationStrategy
from .concatenation import ConcatenationStrategy
from .date_interval import DateIntervalCollectionStrategy
from .engagement_description import EngagementDescriptionStrategy
from .enhanced_date_interval import EnhancedDateIntervalCollectionStrategy
from .enhanced_merge_unique import EnhancedMergeUniqueValuesStrategy
from .enhanced_priority_override import EnhancedPriorityOverrideStrategy
from .kx_dash_priority import KXDashPriorityStrategy
from .merge_unique import MergeUniqueValuesStrategy
from .priority_override import PriorityOverrideStrategy
from .team_roles_strategy import TeamRolesAggregationStrategy


StrategyType = (
    DateIntervalCollectionStrategy
    | MergeUniqueValuesStrategy
    | PriorityOverrideStrategy
    | ConcatenationStrategy
    | EnhancedMergeUniqueValuesStrategy
    | EnhancedPriorityOverrideStrategy
    | EnhancedDateIntervalCollectionStrategy
    | TeamRolesAggregationStrategy
    | KXDashPriorityStrategy
    | EngagementDescriptionStrategy
)

__all__ = [
    'BaseAggregationStrategy',
    'DateIntervalCollectionStrategy',
    'MergeUniqueValuesStrategy',
    'PriorityOverrideStrategy',
    'ConcatenationStrategy',
    'EnhancedMergeUniqueValuesStrategy',
    'EnhancedPriorityOverrideStrategy',
    'EnhancedDateIntervalCollectionStrategy',
    'TeamRolesAggregationStrategy',
    'KXDashPriorityStrategy',
    'EngagementDescriptionStrategy',
    'StrategyType',
]
