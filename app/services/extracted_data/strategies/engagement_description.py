"""Strategy for engagement description fields that returns the first value."""

from schemas import ExtractedData

from .base import BaseAggregationStrategy


class EngagementDescriptionStrategy(BaseAggregationStrategy):
    """Strategy for engagement description fields that returns the first value."""

    def __init__(self):
        # Engagement Description fields
        self._business_issues: list[str] = []
        self._scope_approach: list[str] = []
        self._value_delivered: list[str] = []
        self._engagement_summary: list[str] = []
        self._one_line_description: list[str] = []

    def process(self, extracted_data: ExtractedData) -> None:
        """Process engagement description fields from extracted data."""
        if extracted_data.business_issues:
            self._add_unique_content(self._business_issues, extracted_data.business_issues)
        if extracted_data.scope_approach:
            self._add_unique_content(self._scope_approach, extracted_data.scope_approach)
        if extracted_data.value_delivered:
            self._add_unique_content(self._value_delivered, extracted_data.value_delivered)
        if extracted_data.engagement_summary:
            self._add_unique_content(self._engagement_summary, extracted_data.engagement_summary)
        if extracted_data.one_line_description:
            self._add_unique_content(self._one_line_description, extracted_data.one_line_description)

    def get_business_issues(self) -> str | None:
        """Get first business issues value."""
        return self._get_first_value(self._business_issues)

    def get_scope_approach(self) -> str | None:
        """Get first scope approach value."""
        return self._get_first_value(self._scope_approach)

    def get_value_delivered(self) -> str | None:
        """Get first value delivered."""
        return self._get_first_value(self._value_delivered)

    def get_engagement_summary(self) -> str | None:
        """Get first engagement summary."""
        return self._get_first_value(self._engagement_summary)

    def get_one_line_description(self) -> str | None:
        """Get first one line description."""
        return self._get_first_value(self._one_line_description)

    def _add_unique_content(self, target_list: list[str], content: str) -> None:
        """Add content to list if not already present (case-insensitive)."""
        content = content.strip()
        if content and content.lower() not in [item.lower() for item in target_list]:
            target_list.append(content)

    def _get_first_value(self, items: list[str]) -> str | None:
        """Get first value from list, return None if empty."""
        if not items:
            return None
        return items[0]
