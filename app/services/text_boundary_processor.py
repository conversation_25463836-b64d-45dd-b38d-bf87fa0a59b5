import logging
import re
from typing import Any


__all__ = ['TextBoundaryProcessor']


logger = logging.getLogger(__name__)


class TextBoundaryProcessor:
    """
    Processes text selections to ensure proper word boundaries and spacing.

    This class addresses the text rewriting bug where partial word selections
    lead to merged words and missing spaces in the final output.
    """

    # Regex patterns for word boundary detection
    WORD_CHAR_PATTERN = re.compile(r'\w')
    WORD_BOUNDARY_PATTERN = re.compile(r'\b')
    PUNCTUATION_PATTERN = re.compile(r'[,.!?;:]')
    WHITESPACE_PATTERN = re.compile(r'\s')
    HYPHENATED_WORD_PATTERN = re.compile(r'\w+-\w+')
    SENTENCE_END_PATTERN = re.compile(r'[.!?]\s*')

    def analyze_selection_boundaries(self, full_text: str, snippet: str, snippet_index: int) -> dict[str, Any]:
        """
        Analyze text selection boundaries to detect mid-word selections.

        Args:
            full_text: The complete text containing the snippet
            snippet: The selected text snippet
            snippet_index: The starting index of the snippet in full_text

        Returns:
            Dictionary containing boundary analysis results
        """
        if snippet_index == -1:
            return {
                'is_valid_selection': False,
                'starts_mid_word': False,
                'ends_mid_word': False,
                'needs_expansion': False,
                'expanded_snippet': snippet,
                'expanded_start_index': 0,
                'reason': 'Snippet not found in full text',
            }

        snippet_end_index = snippet_index + len(snippet)

        # Check if selection starts mid-word
        starts_mid_word = self._starts_mid_word(full_text, snippet_index)

        # Check if selection ends mid-word
        ends_mid_word = self._ends_mid_word(full_text, snippet_end_index)

        # Determine if expansion is needed
        needs_expansion = starts_mid_word or ends_mid_word

        # Calculate expanded boundaries if needed
        expanded_snippet = snippet
        expanded_start_index = snippet_index

        if needs_expansion:
            expanded_start_index, expanded_end_index = self._calculate_expanded_boundaries(
                full_text, snippet_index, snippet_end_index
            )
            expanded_snippet = full_text[expanded_start_index:expanded_end_index]

        # Check if expanded snippet starts at sentence beginning
        starts_sentence = self._is_sentence_start(full_text, expanded_start_index)
        original_first_char_capitalized = expanded_snippet and expanded_snippet[0].isupper()

        # Track original trailing spaces for preservation
        original_trailing_spaces = self._extract_trailing_spaces(snippet)

        return {
            'is_valid_selection': True,
            'starts_mid_word': starts_mid_word,
            'ends_mid_word': ends_mid_word,
            'needs_expansion': needs_expansion,
            'expanded_snippet': expanded_snippet,
            'expanded_start_index': expanded_start_index,
            'original_snippet': snippet,
            'original_start_index': snippet_index,
            'starts_sentence': starts_sentence,
            'original_first_char_capitalized': original_first_char_capitalized,
            'original_trailing_spaces': original_trailing_spaces,
            'reason': self._get_analysis_reason(starts_mid_word, ends_mid_word),
        }

    def process_text_for_editing(
        self, full_text: str, snippet: str, result_length: int | None = None
    ) -> dict[str, Any]:
        """
        Process text selection for editing operations with boundary analysis.

        Args:
            full_text: The complete text containing the snippet
            snippet: The selected text snippet to be edited
            result_length: Optional maximum length constraint

        Returns:
            Dictionary containing processed text data for AI editing
        """
        snippet_index = full_text.find(snippet)

        if snippet_index == -1:
            logger.warning('Snippet not found in full_text; returning original text unchanged.')
            return {
                'full_text': full_text,
                'snippet': snippet,
                'text_before_snippet': '',
                'text_after_snippet': '',
                'result_length': result_length,
                'boundary_analysis': {
                    'is_valid_selection': False,
                    'needs_expansion': False,
                    'reason': 'Snippet not found in full text',
                },
            }

        # Analyze boundaries
        boundary_analysis = self.analyze_selection_boundaries(full_text, snippet, snippet_index)

        # Use expanded snippet if boundary expansion is needed
        if boundary_analysis['needs_expansion']:
            processed_snippet = boundary_analysis['expanded_snippet']
            processed_start_index = boundary_analysis['expanded_start_index']

            logger.info(f"Expanded snippet from '{snippet}' to '{processed_snippet}' to maintain word boundaries")
        else:
            processed_snippet = snippet
            processed_start_index = snippet_index

        # Calculate text boundaries
        text_before_snippet = full_text[:processed_start_index]
        text_after_snippet = full_text[processed_start_index + len(processed_snippet) :]

        return {
            'full_text': full_text,
            'snippet': processed_snippet,
            'text_before_snippet': text_before_snippet,
            'text_after_snippet': text_after_snippet,
            'result_length': result_length,
            'boundary_analysis': boundary_analysis,
        }

    def validate_reconstructed_text(
        self, text_before: str, new_snippet: str, text_after: str, boundary_analysis: dict[str, Any] | None = None
    ) -> tuple[str, bool]:
        """
        Validate and fix spacing issues and capitalization in reconstructed text.

        Args:
            text_before: Text before the snippet
            new_snippet: The new/rewritten snippet
            text_after: Text after the snippet
            boundary_analysis: Optional boundary analysis data containing capitalization info

        Returns:
            Tuple of (corrected_text, was_corrected)
        """
        reconstructed = text_before + new_snippet + text_after
        original_reconstructed = reconstructed

        # Check for common spacing issues and fix them
        corrected_text = self._fix_spacing_issues(reconstructed)

        # Preserve trailing spaces if needed
        if boundary_analysis:
            corrected_text = self.preserve_trailing_spaces(
                text_before,
                corrected_text[len(text_before) : len(corrected_text) - len(text_after)],
                text_after,
                boundary_analysis,
            )

        # Preserve sentence capitalization if needed
        if boundary_analysis:
            # Extract the corrected snippet from the corrected text
            corrected_snippet = corrected_text[len(text_before) : len(corrected_text) - len(text_after)]
            corrected_text = self.preserve_sentence_capitalization(
                text_before, corrected_snippet, text_after, boundary_analysis
            )

        was_corrected = corrected_text != original_reconstructed

        if was_corrected:
            logger.info('Fixed spacing and/or capitalization issues in reconstructed text')

        return corrected_text, was_corrected

    def _starts_mid_word(self, text: str, index: int) -> bool:
        """Check if the given index starts in the middle of a word."""
        if index == 0:
            return False

        # Check if the character before the index is a word character
        # and the character at the index is also a word character
        char_before = text[index - 1] if index > 0 else ''
        char_at = text[index] if index < len(text) else ''

        return bool(self.WORD_CHAR_PATTERN.match(char_before)) and bool(self.WORD_CHAR_PATTERN.match(char_at))

    def _ends_mid_word(self, text: str, index: int) -> bool:
        """Check if the given index ends in the middle of a word."""
        if index >= len(text):
            return False

        # Check if the character at the index is a word character
        # and the character before the index is also a word character
        char_at = text[index] if index < len(text) else ''
        char_before = text[index - 1] if index > 0 else ''

        return bool(self.WORD_CHAR_PATTERN.match(char_at)) and bool(self.WORD_CHAR_PATTERN.match(char_before))

    def _calculate_expanded_boundaries(self, text: str, start_index: int, end_index: int) -> tuple[int, int]:
        """
        Calculate expanded boundaries to include complete words.

        Args:
            text: The full text
            start_index: Original start index
            end_index: Original end index

        Returns:
            Tuple of (expanded_start_index, expanded_end_index)
        """
        expanded_start = start_index
        expanded_end = end_index

        # Expand start boundary to include complete word
        while expanded_start > 0 and bool(self.WORD_CHAR_PATTERN.match(text[expanded_start - 1])):
            expanded_start -= 1

        # Expand end boundary to include complete word
        while expanded_end < len(text) and bool(self.WORD_CHAR_PATTERN.match(text[expanded_end])):
            expanded_end += 1

        return expanded_start, expanded_end

    def _get_analysis_reason(self, starts_mid_word: bool, ends_mid_word: bool) -> str:
        """Get a human-readable reason for the boundary analysis result."""
        if starts_mid_word and ends_mid_word:
            return 'Selection starts and ends mid-word'
        elif starts_mid_word:
            return 'Selection starts mid-word'
        elif ends_mid_word:
            return 'Selection ends mid-word'
        else:
            return 'Selection has proper word boundaries'

    def _fix_spacing_issues(self, text: str) -> str:
        """
        Fix common spacing issues in reconstructed text.

        Args:
            text: The text to fix

        Returns:
            Text with spacing issues corrected
        """
        # Fix missing spaces after punctuation
        text = re.sub(r'([,.!?;:])([A-Za-z])', r'\1 \2', text)

        # Fix multiple consecutive spaces
        text = re.sub(r'\s+', ' ', text)

        # Fix spaces before punctuation (except for special cases)
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)

        return text

    def _is_sentence_start(self, text: str, index: int) -> bool:
        """
        Check if the given index is at the beginning of a sentence.

        Args:
            text: The full text
            index: The index to check

        Returns:
            True if the index is at the beginning of a sentence
        """
        if index == 0:
            return True

        # Look backwards to find the last non-whitespace character
        last_non_whitespace_index = -1
        for i in range(index - 1, -1, -1):
            char = text[i]
            if not char.isspace():
                last_non_whitespace_index = i
                break

        if last_non_whitespace_index == -1:
            # If we only found whitespace before reaching the start, it's sentence start
            return True

        last_char = text[last_non_whitespace_index]

        # Check if it's a sentence-ending punctuation
        if last_char in '!?':
            return True
        elif last_char == '.':
            # For periods, we need to be more careful about abbreviations
            # Look for patterns like "U.S." or "Dr." or "etc."
            if last_non_whitespace_index > 0:
                char_before_period = text[last_non_whitespace_index - 1]

                # Check for multiple consecutive periods (ellipsis or abbreviations like "U.S.")
                if char_before_period == '.':
                    return False

                # Check for single uppercase letter abbreviations like "U." in "U.S."
                if char_before_period.isupper() and (
                    last_non_whitespace_index == 1 or text[last_non_whitespace_index - 2].isspace()
                ):
                    return False

                # Check for common abbreviation patterns by looking at the word before the period
                # Find the start of the word containing the period
                word_start = last_non_whitespace_index - 1
                while word_start > 0 and not text[word_start - 1].isspace():
                    word_start -= 1

                word_with_period = text[word_start : last_non_whitespace_index + 1]

                # Common abbreviations that shouldn't end sentences
                common_abbreviations = {
                    'Dr.',
                    'Mr.',
                    'Mrs.',
                    'Ms.',
                    'Prof.',
                    'Sr.',
                    'Jr.',
                    'U.S.',
                    'U.K.',
                    'U.N.',
                    'E.U.',
                    'etc.',
                    'vs.',
                    'i.e.',
                    'e.g.',
                }

                if word_with_period in common_abbreviations:
                    return False

            return True

        return False

    def preserve_sentence_capitalization(
        self, text_before: str, new_snippet: str, text_after: str, boundary_analysis: dict[str, Any]
    ) -> str:
        """
        Preserve sentence capitalization based on boundary analysis.

        Args:
            text_before: Text before the snippet
            new_snippet: The new/rewritten snippet
            text_after: Text after the snippet
            boundary_analysis: Boundary analysis containing capitalization info

        Returns:
            Corrected text with proper capitalization
        """
        # Check if we need to preserve sentence capitalization
        starts_sentence = boundary_analysis.get('starts_sentence', False)
        original_first_char_capitalized = boundary_analysis.get('original_first_char_capitalized', False)

        if starts_sentence and original_first_char_capitalized and new_snippet:
            # Ensure the first character of the new snippet is capitalized
            if new_snippet[0].islower():
                new_snippet = new_snippet[0].upper() + new_snippet[1:]
                logger.info('Preserved sentence-initial capitalization')

        return text_before + new_snippet + text_after

    def _extract_trailing_spaces(self, text: str) -> str:
        """
        Extract trailing spaces from text.

        Args:
            text: The text to analyze

        Returns:
            String containing only the trailing spaces
        """
        if not text:
            return ''

        trailing_spaces = ''
        for i in range(len(text) - 1, -1, -1):
            if text[i].isspace():
                trailing_spaces = text[i] + trailing_spaces
            else:
                break

        return trailing_spaces

    def preserve_trailing_spaces(
        self, text_before: str, new_snippet: str, text_after: str, boundary_analysis: dict[str, Any]
    ) -> str:
        """
        Preserve trailing spaces from the original selection.

        Args:
            text_before: Text before the snippet
            new_snippet: The new/rewritten snippet
            text_after: Text after the snippet
            boundary_analysis: Boundary analysis containing original trailing spaces

        Returns:
            Corrected text with preserved trailing spaces
        """
        original_trailing_spaces = boundary_analysis.get('original_trailing_spaces', '')

        if original_trailing_spaces:
            # Check if the new snippet already has trailing spaces
            new_snippet_trailing_spaces = self._extract_trailing_spaces(new_snippet)

            # If the new snippet doesn't have the same trailing spaces as the original,
            # we need to preserve the original trailing spaces
            if new_snippet_trailing_spaces != original_trailing_spaces:
                # Remove any existing trailing spaces from new snippet
                new_snippet_trimmed = new_snippet.rstrip()
                # Add the original trailing spaces
                new_snippet = new_snippet_trimmed + original_trailing_spaces
                logger.info('Preserved original trailing spaces in rewritten text')

        return text_before + new_snippet + text_after
