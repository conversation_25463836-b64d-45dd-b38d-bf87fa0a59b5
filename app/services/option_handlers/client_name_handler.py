import logging
from uuid import UUI<PERSON>

from constants.extracted_data import ConfirmedD<PERSON><PERSON>ields, ConversationState
from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from repositories import ConversationRepository
from schemas import ClientNameOption, MessageValidator
from services.extracted_data import ExtractedDataService

from .base import BaseOptionHandler


__all__ = ['ClientNameOptionHandlerService']


logger = logging.getLogger(__name__)


class ClientNameOptionHandlerService(BaseOptionHandler[ClientNameOption]):
    """Service for handling client name option selections."""

    def __init__(
        self,
        extracted_data_service: ExtractedDataService,
        conversation_repository: ConversationRepository,
    ):
        self.extracted_data_service = extracted_data_service
        self.conversation_repository = conversation_repository

    async def handle(
        self,
        selected_option: ClientNameOption,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID
            token: The user's token

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        if token is None:
            raise ValueError('Token is required for client name handling')

        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id
            )

            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name=str(ConfirmedDataFields.CLIENT_NAME),
                field_value=selected_option.client_name,
                state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
            )

            # Create confirmation message
            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=selected_option.client_name)

            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
            if confirmed_data.required_fields_except_client_name_are_complete:
                reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                confirmation_message = ' '.join((confirmation_message, reply_type.message_text))

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:  # pragma: no cover
            logger.error('Error handling client name selection: %s', e)
            raise
