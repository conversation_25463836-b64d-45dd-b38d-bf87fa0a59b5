import logging

from constants.extracted_data import Confirmed<PERSON><PERSON><PERSON>ields
from core.utils import clean_string_from_trailing_dot
from schemas import ConfirmedData, NewConfirmedDataForUpdate


logger = logging.getLogger(__name__)


def check_confirmed_fields_replacement(
    *,
    current_confirmed_data: ConfirmedData,
    new_confirmed_data_to_update: NewConfirmedDataForUpdate,
    no_check_objective_and_outcomes: bool = False,
) -> list[ConfirmedDataFields]:
    """
    This checks if the new extracted data might override any fields that the user has already confirmed.
    """

    fields_being_replaced: list[ConfirmedDataFields] = []

    current_confirmed_client_name = current_confirmed_data.client_name
    current_confirmed_ldmf_country = current_confirmed_data.ldmf_country
    current_confirmed_objectives = current_confirmed_data.objective_and_scope
    current_confirmed_outcomes = current_confirmed_data.outcomes

    new_client_names = new_confirmed_data_to_update.client_names
    new_ldmf_countries = new_confirmed_data_to_update.ldmf_countries
    new_objectives = new_confirmed_data_to_update.objective_and_scope
    new_outcomes = new_confirmed_data_to_update.outcomes

    # Client names
    if ConfirmedD<PERSON><PERSON>ields.CLIENT_NAME.is_prohibited_from_replacement:
        if current_confirmed_client_name and new_client_names:
            # If user has confirmed a client name, we shouldn't add any new ones
            # TODO: check this. use forloop?
            if current_confirmed_client_name not in new_client_names:
                logger.info(
                    'Confirmed client name `%s` might be replaced by new client names: %s',
                    current_confirmed_client_name,
                    new_client_names,
                )
                fields_being_replaced.append(ConfirmedDataFields.CLIENT_NAME)

    # LDMF countries
    if ConfirmedDataFields.LDMF_COUNTRY.is_prohibited_from_replacement:
        if current_confirmed_ldmf_country and new_ldmf_countries:
            # If user has confirmed an LDMF country, we shouldn't add any new ones
            # TODO: check this. use forloop?
            if current_confirmed_ldmf_country not in new_ldmf_countries:
                logger.info(
                    'Confirmed LDMF country `%s` might be replaced by new countries: %s',
                    current_confirmed_ldmf_country,
                    new_ldmf_countries,
                )
                fields_being_replaced.append(ConfirmedDataFields.LDMF_COUNTRY)

    if no_check_objective_and_outcomes:
        return fields_being_replaced

    # Objective and scope - Allow updates if confirmed data is empty
    if ConfirmedDataFields.OBJECTIVE_AND_SCOPE.is_prohibited_from_replacement:
        # Only flag as replacement if there's already confirmed data
        if current_confirmed_objectives and new_objectives:
            confirmed_objectives_clean = clean_string_from_trailing_dot(current_confirmed_objectives)
            new_objectives_clean = clean_string_from_trailing_dot(new_objectives)
            if confirmed_objectives_clean != new_objectives_clean:
                # Allow updates for objectives - don't flag as prohibited replacement
                logger.info(
                    'Allowing objective update from `%s` to `%s` as per business requirements',
                    current_confirmed_objectives,
                    new_objectives,
                )
                # Don't add to fields_being_replaced to allow the update

    # Outcomes - Allow updates if confirmed data is empty
    if ConfirmedDataFields.OUTCOMES.is_prohibited_from_replacement:
        # Only flag as replacement if there's already confirmed data
        if current_confirmed_outcomes and new_outcomes:
            confirmed_outcomes_clean = clean_string_from_trailing_dot(current_confirmed_outcomes)
            new_outcomes_clean = clean_string_from_trailing_dot(new_outcomes)
            if confirmed_outcomes_clean != new_outcomes_clean:
                # Allow updates for outcomes - don't flag as prohibited replacement
                logger.info(
                    'Allowing outcomes update from `%s` to `%s` as per business requirements',
                    current_confirmed_outcomes,
                    new_outcomes,
                )
                # Don't add to fields_being_replaced to allow the update

    return fields_being_replaced
