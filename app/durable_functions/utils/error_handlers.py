"""
Utility functions for handling errors consistently across orchestrators.
"""

import logging
from typing import Any, Generator

import azure.durable_functions as df

from constants.durable_functions import ActivityName, EventType, ProcessingStatus
from constants.extracted_data import ConfirmedDataFields, ConversationState
from constants.message import SystemReplyType
from durable_functions.activities.models import (
    SaveExtractionDataActivityOutput,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)
from durable_functions.utils.models import EngagementPeriod


def run_saved_extracted_data_post_processing(
    *,
    save_extraction_data_output: SaveExtractionDataActivityOutput,
    context: df.DurableOrchestrationContext,
    message_id: str,
    signalr_user_id: str,
    logger: logging.Logger,
) -> Generator[Any, Any, None]:
    """
    Helper function to handle ConfirmedDataReplacementError and date confirmation consistently across orchestrators.
    """

    # Handle date confirmation case first
    if save_extraction_data_output.date_confirmation_needed:
        logger.info(f'Date confirmation needed for message {message_id}')
        yield from _handle_date_confirmation_needed(
            context=context,
            message_id=message_id,
            signalr_user_id=signalr_user_id,
            new_date_periods=save_extraction_data_output.new_date_periods,
            logger=logger,
        )
        return

    replaced_confirmed_fields: list[ConfirmedDataFields] | None = save_extraction_data_output.replaced_confirmed_fields
    if not replaced_confirmed_fields:
        return

    reply_type = SystemReplyType.CONFIRMED_FIELDS_CHANGE_PROHIBITED
    error_msg = reply_type.message_text
    error_type = reply_type.value
    affected_fields = [f.value for f in replaced_confirmed_fields]

    # Update processing status
    upd_res = yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.ConfirmedFieldsChangeProhibited,
            message=error_msg,
            metadata={'error_type': error_type, 'affected_fields': affected_fields},
        ),
    )
    logger.info(f'upd_res in run_saved_extracted_data_post_processing: {upd_res}')

    # Send notification about prohibited field changes
    notif_res = yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.UnifiedProcessingError,
            data={
                'message_id': message_id,
                'error': error_msg,
                'error_reason': error_type,
                'affected_fields': affected_fields,
            },
            signalr_user_id=signalr_user_id,
        ),
    )
    logger.info(f'notif_res in run_saved_extracted_data_post_processing: {notif_res}')


def _handle_date_confirmation_needed(
    *,
    context: df.DurableOrchestrationContext,
    message_id: str,
    signalr_user_id: str,
    new_date_periods: list[EngagementPeriod] | None,
    logger: logging.Logger,
) -> Generator[Any, Any, None]:
    """
    Handle the case where date confirmation is needed due to new unambiguous dates from document.

    This triggers a system message with COLLECTING_DATES state and the new dates as options,
    allowing the user to confirm or reject the new date values.
    """
    if not new_date_periods:
        logger.warning(f'Date confirmation needed but no new_date_periods provided for message {message_id}')
        return

    # Get the first period (assuming single date range for now)
    new_period = new_date_periods[0]
    if not new_period.start_date or not new_period.end_date:
        logger.warning(f'Date confirmation needed but incomplete date period for message {message_id}')
        return

    # Update processing status to indicate date confirmation is needed
    upd_res = yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.DateConfirmationNeeded,
            message=f'New dates found in document: {new_period.start_date} to {new_period.end_date}. Please confirm.',
            metadata={
                'conversation_state': ConversationState.COLLECTING_DATES.value,
                'new_start_date': new_period.start_date,
                'new_end_date': new_period.end_date,
                'date_options': [(new_period.start_date, new_period.end_date)],
            },
        ),
    )
    logger.info(f'Date confirmation status update result: {upd_res}')

    # Send notification about date confirmation needed
    notif_res = yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.DateConfirmationNeeded,
            data={
                'message_id': message_id,
                'new_start_date': new_period.start_date,
                'new_end_date': new_period.end_date,
                'message': f'New dates found in document: {new_period.start_date} to {new_period.end_date}. Please confirm.',
                'conversation_state': ConversationState.COLLECTING_DATES.value,
                'options': [(new_period.start_date, new_period.end_date)],
            },
            signalr_user_id=signalr_user_id,
        ),
    )
    logger.info(f'Date confirmation notification result: {notif_res}')
