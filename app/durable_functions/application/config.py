import logging
import os

from constants.environment import Environment
from durable_functions.application.settings import (
    ENVIRONMENT,
    DatabaseSettings,
    OpenAISettings,
    Settings,
)


logger = logging.getLogger(__name__)


settings = Settings(
    db=DatabaseSettings(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        user=os.environ['DB_USER'],
        password=os.environ['DB_PASSWORD'],
        name=('test_' if ENVIRONMENT == Environment.TEST else '') + os.environ['DB_NAME'],
        driver=os.environ.get('DB_DRIVER', 'ODBC+Driver+17+for+SQL+Server'),
    ),
    openai=OpenAISettings(
        endpoint=os.environ['AZURE_OPENAI_ENDPOINT'],
        key=os.environ['AZURE_OPENAI_KEY'],
        model=os.environ['AZURE_OPENAI_MODEL'],
        api_version=os.environ['AZURE_OPENAI_API_VERSION'],
        default_temperature=float(os.environ.get('AZURE_OPENAI_DEFAULT_TEMPERATURE', 0.0)),
        max_tokens=int(os.environ.get('AZURE_OPENAI_MAX_TOKENS', 2000)),
    ),
)  # type: ignore
