from typing import Any

from pydantic import Field, HttpUrl, model_validator

from constants.durable_functions import EventType, ExtractStatus, OrchestratorInputType
from constants.extracted_data import ConfirmedDataFields, DataSourceType, EnhancedExtractionField
from durable_functions.utils import DFBaseModel
from durable_functions.utils.models import (
    EngagementFeeResult,
    EngagementPeriod,
    FinalExtractionDataResults,
    LLMConflictDetected,
    LLMExtractedDataResult,
    TeamRolesExtractionResult,
)


__all__ = [
    'SendNotificationActivityInput',
    'UpdateProcessingStatusActivityInput',
    'ExtractDocumentTextActivityInput',
    'ChunkDocumentActivityInput',
    'ChunkDocumentActivityOutput',
    'SendQueueMessageActivityInput',
    'ReadPromptActivityInput',
    'ExtractDataActivityInput',
    'SaveExtractionDataActivityInput',
    'SaveExtractionDataActivityOutput',
    'AggregateMultiSourceDataActivityInput',
    'SaveAggregatedResultsToBlobActivityInput',
    'SaveConflictsActivityInput',
    'SendFinalQueueMessageActivityInput',
    'EnhancedExtractionActivityInput',
    'EnhancedExtractionActivityOutput',
    'SaveExtractionResultsToBlobActivityInput',
    'ListBlobsActivityInput',
    'ListBlobsActivityOutput',
    'BaseExtractionResult',
    'ProjectRolesExtractionResult',
    'FieldExtractionTask',
    'ProcessEngagementFeeActivityInput',
]


class SendNotificationActivityInput(DFBaseModel):
    event_type: EventType
    data: dict[str, Any]
    signalr_user_id: str | None = None


class UpdateProcessingStatusActivityInput(DFBaseModel):
    message_id: str
    status: str
    message: str
    metadata: dict[str, Any] = {}


class ExtractDocumentTextActivityInput(DFBaseModel):
    blob_url: str
    message_id: str
    file_name: str


class BaseExtractDocumentTextActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    status: ExtractStatus


class ExtractDocumentTextActivityOutput(BaseExtractDocumentTextActivityOutput):
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ExtractDocumentTextActivityOutputFailed(BaseExtractDocumentTextActivityOutput):
    error: str
    file_is_corrupted: bool | None = None


class ChunkDocumentActivityInput(DFBaseModel):
    message_id: str
    file_name: str
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ChunkDocumentActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    chunk_count: int
    chunk_urls: list[dict[str, Any]]


class SendQueueMessageActivityInput(DFBaseModel):
    message_id: str
    file_name: str
    chunk_count: int
    chunk_urls: list[dict[str, Any]]
    signalr_user_id: str
    input_type: OrchestratorInputType = OrchestratorInputType.Document


class ReadPromptActivityInput(DFBaseModel):
    prompt_url: str


class ExtractDataActivityInput(DFBaseModel):
    chunk_url: HttpUrl | None = None
    text_content: str | None = None


class SaveExtractionDataActivityInput(DFBaseModel):
    message_id: str
    extraction_data: FinalExtractionDataResults
    data_source_type: DataSourceType


class SaveExtractionDataActivityOutput(DFBaseModel):
    extracted_data_was_updated: bool
    replaced_confirmed_fields: list[ConfirmedDataFields] | None = None
    date_confirmation_needed: bool = False
    new_date_periods: list[EngagementPeriod] | None = None  # EngagementPeriod list for date confirmation


class AggregateMultiSourceDataActivityInput(DFBaseModel):
    message_id: str
    source_results: list[tuple[DataSourceType, FinalExtractionDataResults]]


class SaveAggregatedResultsToBlobActivityInput(DFBaseModel):
    message_id: str
    aggregated_data: FinalExtractionDataResults


class SendFinalQueueMessageActivityInput(DFBaseModel):
    message_id: str
    blob_url: str
    signalr_user_id: str


class ProcessEngagementFeeActivityInput(DFBaseModel):
    """Input model for processing engagement fee data."""

    conversation_id: str
    engagement_fees: list[EngagementFeeResult]


class BaseExtractionResult(DFBaseModel):
    value: str | None = Field(default=None, description='Extracted value')


class ListExtractionResult(DFBaseModel):
    value: list[str] | None = Field(default=None, description='Extracted values')


class ProjectRolesExtractionResult(DFBaseModel):
    value: list[TeamRolesExtractionResult] | None = Field(default=None, description='Project roles')

    @model_validator(mode='after')
    def validate_project_roles(self) -> 'ProjectRolesExtractionResult':
        if self.value:
            self.value = [TeamRolesExtractionResult.model_validate(role) for role in self.value]
        return self


class FieldExtractionTask(DFBaseModel):
    field_name: EnhancedExtractionField
    context: str
    system_prompt: str
    user_prompt: str
    max_tokens: int = 2000


class EnhancedExtractionActivityInput(DFBaseModel):
    """Input model for enhanced field extraction activity."""

    conversation_id: str
    tasks: list['FieldExtractionTask']
    source: str


class EnhancedExtractionActivityOutput(DFBaseModel):
    """Output model for enhanced extraction activity."""

    conversation_id: str
    source: str
    extracted_data: FinalExtractionDataResults


class SaveExtractionResultsToBlobActivityInput(DFBaseModel):
    message_id: str
    extract_data_results: list[LLMExtractedDataResult]
    source: str


class ListBlobsActivityInput(DFBaseModel):
    prefix: str


class ListBlobsActivityOutput(DFBaseModel):
    source: str
    chunks: list[LLMExtractedDataResult]


class SaveConflictsActivityInput(DFBaseModel):
    conflicts: list[LLMConflictDetected]
    message_id: str
