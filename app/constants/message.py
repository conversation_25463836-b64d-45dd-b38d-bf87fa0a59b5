import logging

from core.enum import StrEnum
from core.utils import load_file_from_folder

from .engagement import engagement_templates


__all__ = [
    'MessageRole',
    'MessageType',
    'OptionType',
    'PageType',
    'WELCOME_MESSAGE',
    'ConversationMessageIntention',
    'SuggestedUserPrompt',
    'SingularGrammarForms',
    'PluralGrammarForms',
    'DASH_TASK_SELECTED_TEMPLATE',
    'BRIEF_DESCRIPTION_REPLY',
    'EXAMPLE_REPLY',
    'NEED_INFO_OUTCOMES',
    'NEED_INFO_OBJECTIVE_SCOPE',
    'DASH_TASK_SELECTED_TEMPLATE',
    'WELCOME_MESSAGE_WITH_ONE_DASH_TASK',
    'WELCOME_MESSAGE_WITH_MANY_DASH_TASKS',
    'CLIENT_NAME_MULTIPLE_OPTIONS',
    'CLIENT_NAME_SINGLE_CONFIRMATION',
    'CLIENT_NAME_SINGLE_FOUND_CONFIRMATION',
    'CLIENT_NAME_CONFIRMED',
    'CLIENT_NOT_FOUND',
    'CLIENT_CREATION_CONFIRMED',
    'NEED_INFO_CLIENT_NAME',
    'LDMF_COUNTRY_CONFIRMED',
    'LDMF_COUNTRY_MULTIPLE_OPTIONS',
    'LDMF_COUNTRY_SINGLE_CONFIRMATION',
    'NEED_INFO_LDMF_COUNTRY',
    'NEED_INFO_INITIAL_PROMPT',
    'OBJECTIVE_AND_SCOPE_CONFIRMED',
    'OUTCOMES_CONFIRMED',
    'DATES_CONFIRMED',
    'DATES_AMBIGUOUS',
    'DATES_ONE_DATE',
    'ADDITIONAL_DATA_REPLY',
    'READY_TO_GENERATE_QUAL_REPLY',
    'CONFIRMED_FIELDS_READY',
    'READY_TO_CREATE_DRAFT_QUAL',
    'OUTCOMES_AGGREGATED_QUESTION',
    'ALL_REQUIRED_FIELDS_EXTRACTED_DOCS',
    'EXTRACTED_DATA_DATE_INTERVAL_MISSING',
    'EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING',
    'EXTRACTED_DATA_OUTCOMES_MISSING',
    'CLIENT_NAME_CONFIRMED_LAST_FIELD',
    'THANX_FOR_INFORMATION',
    'CorruptedUtilsTemplate',
    'ALL_REQUIRED_FIELDS_EXTRACTED_DOCS',
    'ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND',
]


logger = logging.getLogger(__name__)


class MessageRole(StrEnum):
    SYSTEM = 'system'
    USER = 'user'


class MessageType(StrEnum):
    TEXT = 'text'
    FILE = 'file'
    FORM = 'form'
    TEXT_WITH_FILE = 'text_with_file'
    ERROR = 'error'


class TextEditCommand(StrEnum):
    EXPAND = 'expand'
    SHORTEN = 'shorten'
    REWRITE = 'rewrite'
    PROMPT = 'prompt'
    STORE = 'store'
    UNDO = 'undo'
    REPLACE_CLIENT_NAME_CONFIRM = 'replace_client_name_confirm'


class QualFieldName(StrEnum):
    TITLE = 'title'
    BUSINESS_ISSUES = 'business_issues'
    SCOPE_APPROACH = 'scope_approach'
    VALUE_DELIVERED = 'value_delivered'
    ENGAGEMENT_SUMMARY = 'engagement_summary'
    ONE_LINE_DESCRIPTION = 'one_line_description'
    CHAT = 'chat'

    @property
    def is_regenerator(self) -> bool:
        return self in {
            QualFieldName.BUSINESS_ISSUES,
            QualFieldName.SCOPE_APPROACH,
            QualFieldName.VALUE_DELIVERED,
        }


class PageType(StrEnum):
    PROMPT = 'prompt'
    ENGAGEMENT_DESCRIPTION = 'engagement_description'
    ENGAGEMENT_DETAILS = 'engagement_details'
    USAGE_AND_TEAM_DETAILS = 'usage_and_team_details'


class CorruptedUtilsTemplate(StrEnum):
    ONE = 'the file'
    ONE_OF_UPLOADED = 'one file'
    TWO_OF_UPLOADED = 'two files'
    ALL = 'all the files'


class SingularGrammarForms(StrEnum):
    NOUNS_SUFFIX = ''
    VERBS_SUFFIX = 's'
    PERSONAL_PRONOUN = 'it'
    DO_VERB = 'does'
    HAVE_VERB = 'has'
    BE_VERB = 'is'


class PluralGrammarForms(StrEnum):
    NOUNS_SUFFIX = 's'
    VERBS_SUFFIX = ''
    PERSONAL_PRONOUN = 'they'
    DO_VERB = 'do'
    HAVE_VERB = 'have'
    BE_VERB = 'are'


WELCOME_MESSAGE = load_file_from_folder('conversation_messages', 'welcome_message.txt')
DASH_TASK_SELECTED_TEMPLATE = load_file_from_folder('conversation_messages', 'dash_task_selected.txt')
BRIEF_DESCRIPTION_REPLY = load_file_from_folder('conversation_messages', 'brief_description_reply.txt')
UNDEFINED_REPLY = load_file_from_folder('conversation_messages', 'undefined_reply.txt')
EXAMPLE_REPLY = load_file_from_folder('conversation_messages', 'example_reply.txt')
NEED_INFO_OUTCOMES = load_file_from_folder('conversation_messages', 'need_info_outcomes.txt')
NEED_INFO_OBJECTIVE_SCOPE = load_file_from_folder('conversation_messages', 'need_info_objective_scope.txt')
WELCOME_MESSAGE_WITH_ONE_DASH_TASK = load_file_from_folder(
    'conversation_messages', 'welcome_message_with_one_dash_task.txt'
)
WELCOME_MESSAGE_WITH_MANY_DASH_TASKS = load_file_from_folder(
    'conversation_messages', 'welcome_message_with_many_dash_tasks.txt'
)

WELCOME_MESSAGE_REMINDER = load_file_from_folder('conversation_messages', 'welcome_message_reminder.txt')
WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED = load_file_from_folder(
    'conversation_messages', 'welcome_message_with_one_dash_selected.txt'
)

CLIENT_NAME_MULTIPLE_OPTIONS = load_file_from_folder('conversation_messages', 'client_name_multiple_options.txt')
CLIENT_NAME_SINGLE_CONFIRMATION = load_file_from_folder('conversation_messages', 'client_name_single_confirmation.txt')
CLIENT_NAME_SINGLE_FOUND_CONFIRMATION = load_file_from_folder(
    'conversation_messages', 'client_name_single_found_confirmation.txt'
)
CLIENT_NAME_SINGLE_CONFIRMATION_SIMPLE = load_file_from_folder(
    'conversation_messages', 'client_name_single_confirmation_simple.txt'
)
CLIENT_NAME_CONFIRMED = load_file_from_folder('conversation_messages', 'client_name_confirmed.txt')
CLIENT_NOT_FOUND = load_file_from_folder('conversation_messages', 'client_not_found.txt')
CLIENT_CREATION_CONFIRMED = load_file_from_folder('conversation_messages', 'client_creation_confirmed.txt')

DATES_CONFIRMED = load_file_from_folder('conversation_messages', 'dates_confirmed.txt')
DATES_AMBIGUOUS = load_file_from_folder('conversation_messages', 'dates_ambiguous.txt')
DATES_UNAMBIGUOUS = load_file_from_folder('conversation_messages', 'dates_unambiguous.txt')
DATES_ONE_DATE = load_file_from_folder('conversation_messages', 'dates_one_date.txt')
NEED_INFO_DATES = load_file_from_folder('conversation_messages', 'need_info_dates.txt')

NEED_INFO_CLIENT_NAME = 'I need the client name to create the qual. Can you provide the client name?'  # it's out of scope of this task, but should provide something to pass tests

LDMF_COUNTRY_MULTIPLE_OPTIONS = load_file_from_folder('conversation_messages', 'ldmf_country_multiple_options.txt')
LDMF_COUNTRY_SINGLE_CONFIRMATION = load_file_from_folder(
    'conversation_messages', 'ldmf_country_single_confirmation.txt'
)
LDMF_COUNTRY_CONFIRMED = load_file_from_folder('conversation_messages', 'ldmf_country_confirmed.txt')
NEED_INFO_LDMF_COUNTRY = 'I need the LDMF country to create the qual. Can you provide the LDMF country?'
NEED_INFO_INITIAL_PROMPT = 'Could you tell more about the client and what service was delivered?'
OBJECTIVE_AND_SCOPE_CONFIRMED = load_file_from_folder('conversation_messages', 'objective_and_scope_confirmed.txt')
OUTCOMES_CONFIRMED = load_file_from_folder('conversation_messages', 'outcomes_confirmed.txt')
NEED_INFO_INITIAL_MISSING_PROMPT = "I'll need some more details to draft your qual. Could you start by telling me about the client and what services we delivered?"
OUTCOMES_AGGREGATED_QUESTION = 'Found these outcomes: {outcomes} can you confirm?'

CONFIRMED_FIELDS_READY = 'Anything else to add?'
READY_TO_CREATE_DRAFT_QUAL = load_file_from_folder('conversation_messages', 'ready_to_create_draft_qual.txt')
ADDITIONAL_DATA_REPLY = load_file_from_folder('conversation_messages', 'additional_data_reply.txt')
READY_TO_GENERATE_QUAL_REPLY = load_file_from_folder('conversation_messages', 'ready_to_generate_qual_reply.txt')
ALL_REQUIRED_FIELDS_EXTRACTED_DOCS = load_file_from_folder('conversation_messages', 'all_required_fields_extracted.txt')
ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND = load_file_from_folder(
    'conversation_messages', 'all_required_fields_extracted_client_not_found.txt'
)


EXTRACTED_LDMF_NOT_VALID = (
    'I couldn’t determine a Lead member firm. Could you tell me who the Lead member firm is for this engagement?'
)
EXTRACTED_DATA_DATE_INTERVAL_MISSING = (
    "Got it! I'll need some more details. Could you tell me the approximate engagement start and end dates?"
)
EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING = (
    "I'll need some more details to draft your qual. Could you tell me more about the project objectives and approach?"
)
EXTRACTED_DATA_OUTCOMES_MISSING = (
    "I'll need some more details to draft your qual. Could you tell me more about the project outcomes?"
)

PROVIDE_CLIENT_NAME_UNSURE = "Okay. Please tell me the client's name."
PROVIDE_CLIENT_NAME_MULTIPLE_NOT_FOUND = (
    "I couldn't find the provided clients in our system. Please tell me the client's name."
)
PROVIDE_CLIENT_NAME_DENIAL = load_file_from_folder('conversation_messages', 'provide_client_name_denial.txt')
CLIENT_NAME_CONFIRMED_LAST_FIELD = (
    "Got it. I'll use “{client_name}”. Anything else to add before I create your draft qual?"
)
THANX_FOR_INFORMATION = 'Thanks for the information.'
CONFIRMED_FIELDS_CHANGE_PROHIBITED = (
    "Sorry, it's not possible to update confirmed information at this stage. "
    'However, you can make changes to any information (except client name) once the draft qual is created. '
    'Alternatively, you may delete this qual and create a new one if needed.'
)

CORRUPTED_FILE_ERROR = load_file_from_folder('conversation_messages', 'corrupted_file_error.txt')


DETECT_LANGUAGE_SYSTEM_PROMPT = load_file_from_folder('translation_prompts', 'language_detect_system_prompt.txt')
DETECT_LANGUAGE_USER_PROMPT = load_file_from_folder('translation_prompts', 'language_detect_user_prompt.txt')
TRANSLATE_TO_ENGLISH_SYSTEM_PROMPT = load_file_from_folder('translation_prompts', 'translate_system_prompt.txt')
TRANSLATE_TO_ENGLISH_USER_PROMPT = load_file_from_folder('translation_prompts', 'translate_user_prompt.txt')
UNCERTAINTY_EMPTY_AGGREGATION = load_file_from_folder('conversation_messages', 'uncertainty_empty_aggregation.txt')

CONFLICT_DETECTED = load_file_from_folder('conversation_messages', 'conflict_detected.txt')


class OptionType(StrEnum):
    DATES = 'dates'
    LDMF_COUNTRY = 'ldmf_country'
    CLIENT_NAME = 'client_name'
    KX_DASH_TASK = 'kx_dash_task'
    CONFLICT = 'conflict'


class ConversationMessageIntention(StrEnum):
    UNDEFINED = 'undefined'

    GENERATE_QUAL = 'generate_qual'
    EXTRACTION = 'extraction'
    EXAMPLE = 'example'
    DASH_DISCARD = 'dash_discard'
    UNCERTAINTY = 'uncertainty'  # NOTE: used to be BRIEF_DESCRIPTION, renamed for better LLM understanding
    NEED_CONTEXT = 'need_context'  # NOTE: mapped to UNCERTAINTY
    USER_CONFIRMATION = 'user_confirmation'
    USER_DENIAL = 'user_denial'
    CHANGE_ENGAGEMENT_DATES = 'change_engagement_dates'
    PROVIDE_CLIENT_NAME = 'provide_client_name'
    MANUAL_LDMF_INPUT = 'manual_ldmf_input'
    NAVIGATE_TO_RESOURCE_PAGE = 'navigate_to_resource_page'
    DISCARD_ERROR_FILE = 'discard_error_file'


class SuggestedUserPrompt(StrEnum):
    """Suggested user replies for the conversation."""

    NO_CREATE_NEW_QUAL = 'No, create a new qual'  # Case 1, 2
    SHOW_ME_AN_EXAMPLE_PROMPT = 'Show me an example prompt'  # Case 3, 4, 5
    NO_CREATE_MY_QUAL = 'No, create my qual'  # Case 6, 7
    ENTER_A_NEW_CLIENT = 'Enter a new client'  # Case 8, 9
    YES_THIS_IS_CORRECT = 'Yes, this is correct'  # Case 10, 11
    NO_I_WILL_ENTER_CLIENT_NAME = "No, I'll enter the client name"  # Case 12, 13
    YES = 'Yes'  # Case 14, 15
    NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM = "No, I'll enter the Lead Deloitte Member Firm"  # Case 16, 17

    # Welcome message suggested prompts
    WRITE_A_BRIEF_DESCRIPTION = 'Write a brief description'
    UPLOAD_DOCUMENT = 'Upload a document'
    CONTINUE_WITHOUT_ERROR_FILE = 'Continue without the error file'

    @staticmethod
    def agreement_prompts() -> set[str]:
        return {
            str(SuggestedUserPrompt.YES_THIS_IS_CORRECT),
            str(SuggestedUserPrompt.YES),
        }

    @staticmethod
    def for_multiple_clients() -> list[str]:
        return [str(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)]

    @staticmethod
    def for_single_client() -> list[str]:
        return [str(SuggestedUserPrompt.YES_THIS_IS_CORRECT), str(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)]


class SystemReplyType(StrEnum):
    """Enum representing the type of system reply."""

    WELCOME_MESSAGE_WITH_MANY_DASH_TASKS = 'welcome_message_with_many_dash_tasks'
    WELCOME_MESSAGE_WITH_ONE_DASH_TASK = 'welcome_message_with_one_dash_task'
    WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED = 'welcome_message_with_one_dash_selected'
    DASH_TASK_SELECTED_TEMPLATE = 'dash_task_selected_template'
    WELCOME_MESSAGE = 'welcome_message'
    WELCOME_MESSAGE_REMINDER = 'welcome_message_reminder'
    WELCOME_MESSAGE_ENGAGEMENT_DETAILS = 'welcome_message_engagement_details'

    EMPTY = 'empty'  # Used only for initial conversation state

    UNDEFINED = 'undefined'
    EXAMPLE = 'example'
    BRIEF_DESCRIPTION = 'brief_description'
    NEED_INFO_INITIAL = 'need_info_initial'
    NEED_INFO_INITIAL_MISSING = 'need_info_initial_missing'

    NEED_INFO_DATES = 'need_info_dates'
    NEED_INFO_LDMF_COUNTRY = 'need_info_ldmf_country'
    NEED_INFO_OUTCOMES = 'need_info_outcomes'
    NEED_INFO_OBJECTIVE_SCOPE = 'need_info_objective_scope'
    NEED_INFO_CLIENT_NAME = 'need_info_client_name'
    OUTCOMES_AGGREGATED_QUESTION = 'outcomes_aggregated_question'
    CONFIRMED_FIELDS_READY = 'confirmed_fields_ready'
    DATES_CONFIRMED = 'dates_confirmed'
    DATES_AMBIGUOUS = 'dates_ambiguous'
    DATES_UNAMBIGUOUS = 'dates_unambiguous'
    ADDITIONAL_DATA = 'additional_data'
    ADDITIONAL_DATA_PROPOSAL = 'additional_data_proposal'
    CLIENT_NAME_CONFIRMED = 'client_name_confirmed'
    CLIENT_NAME_SINGLE_CONFIRMATION = 'client_name_single_confirmation'
    CLIENT_NAME_SINGLE_FOUND_CONFIRMATION = 'client_name_single_found_confirmation'
    CLIENT_NAME_SINGLE_CONFIRMATION_SIMPLE = 'client_name_single_confirmation_simple'
    CLIENT_NOT_FOUND = 'client_name_not_found'
    CLIENT_CREATION_CONFIRMED = 'client_creation_confirmed'
    CLIENT_NAME_MULTIPLE_OPTIONS = 'client_name_multiple_options'
    OBJECTIVE_AND_SCOPE_CONFIRMED = 'objective_and_scope_confirmed'
    CONFIRM_OBJECTIVE_AND_SCOPE = 'confirm_objective_and_scope'
    READY_TO_GENERATE_QUAL_REPLY = 'ready_to_generate_qual_reply'
    OUTCOMES_CONFIRMED = 'outcomes_confirmed'
    LDMF_COUNTRY_CONFIRMED = 'ldmf_country_confirmed'
    LDMF_COUNTRY_SINGLE_CONFIRMATION = 'ldmf_country_single_confirmation'
    LDMF_COUNTRY_MULTIPLE_OPTIONS = 'ldmf_country_multiple_options'
    READY_TO_CREATE_DRAFT_QUAL = 'ready_to_create_draft_qual'
    FIELD_SAVED = 'field_saved'
    CLIENT_CREATION_UNSURE = 'client_creation_unsure'
    CLIENT_CREATION_UNSUCCESSFUL = 'client_creation_unsuccessful'
    CLIENT_CREATION_FAILED = 'client_creation_failed'
    CLIENT_NAME_TOO_LONG = 'client_name_too_long'
    DATES_ONE_DATE = 'dates_one_date'

    EXTRACTED_DATA_DATE_INTERVAL_MISSING = 'extracted_data_date_interval_missing'
    EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING = 'extracted_data_objective_scope_missing'
    EXTRACTED_DATA_OUTCOMES_MISSING = 'extracted_data_outcomes_missing'
    EXTRACTED_DATA_LDMF_MISSING = 'extracted_data_ldmf_missing'
    EXTRACTED_LDMF_NOT_VALID = 'extracted_ldmf_not_valid'

    MISSING_REQUIRED_DATA = 'missing_required_data'
    FORMATTED_EXTRACTED_DATA = 'formatted_extracted_data'

    PROVIDE_CLIENT_NAME_UNSURE = 'provide_client_name_unsure'
    PROVIDE_CLIENT_NAME_MULTIPLE_NOT_FOUND = 'provide_client_name_multiple_not_found'
    PROVIDE_CLIENT_NAME_DENIAL = 'provide_client_name_denial'
    CLIENT_NAME_CONFIRMED_LAST_FIELD = 'client_name_confirmed_last_field'
    CORRUPTED_FILE = 'corrupted_file'
    UNCERTAINTY_EMPTY_AGGREGATION = 'uncertainty_empty_aggregation'
    CONFIRMED_FIELDS_CHANGE_PROHIBITED = 'confirmed_fields_change_prohibited'

    ENGAGEMENT_DETAILS_FIELD_MODIFIED = 'engagement_details_field_modified'
    ENGAGEMENT_DETAILS_FIELD_ERROR = 'engagement_details_field_error'
    ENGAGEMENT_DETAILS_NAVIGATED = 'engagement_details_navigated'
    ENGAGEMENT_DETAILS_UNKNOWN_FIELD = 'engagement_details_unknown_field'
    ENGAGEMENT_DETAILS_UNDEFINED = 'engagement_details_undefined'

    CONFLICT_DETECTED = 'conflict_detected'

    ENGAGEMENT_DETAILS_SORRY_CANT_HELP = 'sorry_cant_help'

    CLIENT_NAME_REPLACED = 'client_name_replaced'
    PAGE_PREFILLED_MESSAGE = 'page_prefilled_message'

    ALL_REQUIRED_FIELDS_EXTRACTED_DOCS = 'all_required_fields_extracted_docs'
    ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND = 'all_required_fields_extracted_client_not_found'
    INVALID_CLIENT_NAME = 'invalid_client_name'

    @staticmethod
    def get_data_complete_replies() -> set['SystemReplyType']:
        return {
            SystemReplyType.ADDITIONAL_DATA,
            SystemReplyType.ADDITIONAL_DATA_PROPOSAL,
            SystemReplyType.CONFIRMED_FIELDS_READY,
        }

    @staticmethod
    def get_data_input_request_replies() -> set['SystemReplyType']:
        return {
            SystemReplyType.NEED_INFO_CLIENT_NAME,
            SystemReplyType.NEED_INFO_LDMF_COUNTRY,
            SystemReplyType.NEED_INFO_DATES,
            SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE,
            SystemReplyType.NEED_INFO_OUTCOMES,
            SystemReplyType.EXTRACTED_DATA_LDMF_MISSING,
            SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING,
            SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING,
            SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING,
        }

    @staticmethod
    def get_data_confirmation_replies() -> set['SystemReplyType']:
        return {
            SystemReplyType.CLIENT_CREATION_CONFIRMED,
            SystemReplyType.LDMF_COUNTRY_CONFIRMED,
            SystemReplyType.DATES_CONFIRMED,
            SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED,
            SystemReplyType.OUTCOMES_CONFIRMED,
        }

    @property
    def message_text(self) -> str:
        return {
            SystemReplyType.WELCOME_MESSAGE_WITH_MANY_DASH_TASKS: WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
            SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_TASK: WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
            SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED: WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED,
            SystemReplyType.DASH_TASK_SELECTED_TEMPLATE: DASH_TASK_SELECTED_TEMPLATE,
            SystemReplyType.WELCOME_MESSAGE: WELCOME_MESSAGE,
            SystemReplyType.WELCOME_MESSAGE_REMINDER: WELCOME_MESSAGE_REMINDER,
            SystemReplyType.EMPTY: '',
            SystemReplyType.UNDEFINED: UNDEFINED_REPLY,
            SystemReplyType.EXAMPLE: EXAMPLE_REPLY,
            SystemReplyType.BRIEF_DESCRIPTION: BRIEF_DESCRIPTION_REPLY,
            SystemReplyType.NEED_INFO_INITIAL: NEED_INFO_INITIAL_PROMPT,
            SystemReplyType.NEED_INFO_INITIAL_MISSING: NEED_INFO_INITIAL_MISSING_PROMPT,
            SystemReplyType.NEED_INFO_DATES: NEED_INFO_DATES,
            SystemReplyType.NEED_INFO_LDMF_COUNTRY: NEED_INFO_LDMF_COUNTRY,
            SystemReplyType.NEED_INFO_OUTCOMES: NEED_INFO_OUTCOMES,
            SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE: NEED_INFO_OBJECTIVE_SCOPE,
            SystemReplyType.NEED_INFO_CLIENT_NAME: NEED_INFO_CLIENT_NAME,
            SystemReplyType.OUTCOMES_AGGREGATED_QUESTION: OUTCOMES_AGGREGATED_QUESTION,
            SystemReplyType.CONFIRMED_FIELDS_READY: CONFIRMED_FIELDS_READY,
            SystemReplyType.DATES_CONFIRMED: DATES_CONFIRMED,
            SystemReplyType.DATES_AMBIGUOUS: DATES_AMBIGUOUS,
            SystemReplyType.DATES_UNAMBIGUOUS: DATES_UNAMBIGUOUS,
            SystemReplyType.ADDITIONAL_DATA: ADDITIONAL_DATA_REPLY,
            SystemReplyType.ADDITIONAL_DATA_PROPOSAL: ('What additional information would you like to provide?'),
            SystemReplyType.CLIENT_NAME_CONFIRMED: CLIENT_NAME_CONFIRMED,
            SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION: CLIENT_NAME_SINGLE_CONFIRMATION,
            SystemReplyType.CLIENT_NAME_SINGLE_FOUND_CONFIRMATION: CLIENT_NAME_SINGLE_FOUND_CONFIRMATION,
            SystemReplyType.CLIENT_NOT_FOUND: CLIENT_NOT_FOUND,
            SystemReplyType.CLIENT_CREATION_CONFIRMED: CLIENT_CREATION_CONFIRMED,
            SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS: CLIENT_NAME_MULTIPLE_OPTIONS,
            SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED: OBJECTIVE_AND_SCOPE_CONFIRMED,
            SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE: (
                'Found this objective and scope: {objective_and_scope}. Can you confirm?'
            ),
            SystemReplyType.READY_TO_GENERATE_QUAL_REPLY: READY_TO_GENERATE_QUAL_REPLY,
            SystemReplyType.OUTCOMES_CONFIRMED: OUTCOMES_CONFIRMED,
            SystemReplyType.LDMF_COUNTRY_CONFIRMED: LDMF_COUNTRY_CONFIRMED,
            SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION: LDMF_COUNTRY_SINGLE_CONFIRMATION,
            SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS: LDMF_COUNTRY_MULTIPLE_OPTIONS,
            SystemReplyType.READY_TO_CREATE_DRAFT_QUAL: READY_TO_CREATE_DRAFT_QUAL,
            SystemReplyType.FIELD_SAVED: "I've saved field.",
            SystemReplyType.CLIENT_CREATION_UNSURE: (
                "I'm not sure what you mean. Would you like to add '{proposed_client_name}' as a new client, "
                'or would you prefer to enter a different client name?'
            ),
            SystemReplyType.CLIENT_CREATION_FAILED: (
                "Sorry, I encountered an error while adding '{proposed_client_name}'. Please try again."
            ),
            SystemReplyType.CLIENT_NAME_TOO_LONG: (
                "It looks like the client's name is not a correct one. Please provide realistic client's name."
            ),
            SystemReplyType.CLIENT_CREATION_UNSUCCESSFUL: (
                "Sorry, I couldn't add '{proposed_client_name}' as a new client. Please try a different name."
            ),
            SystemReplyType.DATES_ONE_DATE: DATES_ONE_DATE,
            SystemReplyType.EXTRACTED_DATA_LDMF_MISSING: EXTRACTED_LDMF_NOT_VALID,
            SystemReplyType.EXTRACTED_LDMF_NOT_VALID: EXTRACTED_LDMF_NOT_VALID,
            SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING: EXTRACTED_DATA_DATE_INTERVAL_MISSING,
            SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING: EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING,
            SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING: EXTRACTED_DATA_OUTCOMES_MISSING,
            SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE: PROVIDE_CLIENT_NAME_UNSURE,
            SystemReplyType.PROVIDE_CLIENT_NAME_MULTIPLE_NOT_FOUND: PROVIDE_CLIENT_NAME_MULTIPLE_NOT_FOUND,
            SystemReplyType.PROVIDE_CLIENT_NAME_DENIAL: PROVIDE_CLIENT_NAME_DENIAL,
            SystemReplyType.CLIENT_NAME_CONFIRMED_LAST_FIELD: CLIENT_NAME_CONFIRMED_LAST_FIELD,
            SystemReplyType.CORRUPTED_FILE: CORRUPTED_FILE_ERROR,
            SystemReplyType.UNCERTAINTY_EMPTY_AGGREGATION: UNCERTAINTY_EMPTY_AGGREGATION,
            SystemReplyType.CONFIRMED_FIELDS_CHANGE_PROHIBITED: CONFIRMED_FIELDS_CHANGE_PROHIBITED,
            SystemReplyType.CONFLICT_DETECTED: CONFLICT_DETECTED,
            SystemReplyType.CLIENT_NAME_REPLACED: engagement_templates.general.client_name_replaced,
            SystemReplyType.PAGE_PREFILLED_MESSAGE: engagement_templates.general.page_prefilled_message,
            SystemReplyType.ALL_REQUIRED_FIELDS_EXTRACTED_DOCS: ALL_REQUIRED_FIELDS_EXTRACTED_DOCS,
            SystemReplyType.ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND: ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND,
            SystemReplyType.INVALID_CLIENT_NAME: "It looks like the client's name is not a correct one. Please provide realistic client's name.",
            SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION_SIMPLE: CLIENT_NAME_SINGLE_CONFIRMATION_SIMPLE,
        }[self]
