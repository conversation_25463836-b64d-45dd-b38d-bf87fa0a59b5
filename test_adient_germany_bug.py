#!/usr/bin/env python3
"""
Test script to reproduce the "Adient Germany" client name truncation bug.

This script tests the client name parsing logic to identify where
"Adient Germany" gets incorrectly truncated to just "Germany".
"""

import re

# Company suffixes from the app
COMPANY_SUFFIXES = ('llc', 'inc', 'co', 'corp', 'ltd', 'limited')


def test_company_suffix_pattern():
    """Test the CLIENT_NAME_PATTERN regex used in client name processing."""
    print("=== Testing CLIENT_NAME_PATTERN Regex ===")
    
    # Pattern from ClientNameHandler
    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?\sand\s" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )
    
    test_cases = [
        "Adient Germany",
        "Adient Germany Inc",
        "Microsoft Corporation",
        "Apple Inc and Google LLC",
        "BMW Germany",
        "Siemens Germany GmbH",
        "Volkswagen Germany",
    ]
    
    for test_case in test_cases:
        matches = CLIENT_NAME_PATTERN.findall(test_case)
        split_result = CLIENT_NAME_PATTERN.split(test_case)
        print(f"Input: '{test_case}'")
        print(f"  Matches: {matches}")
        print(f"  Split result: {split_result}")
        print()


def test_client_name_splitting():
    """Test the _split_compound_client_name method (simplified version)."""
    print("=== Testing Client Name Splitting Logic ===")

    # Simplified version of the splitting logic from ClientNameHandler
    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?\sand\s" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )

    def _split_compound_client_name(client_name: str):
        """Simplified version of the splitting logic."""
        split_result = CLIENT_NAME_PATTERN.split(client_name)
        split_result = list(filter(None, split_result))

        current_split = 0
        last_split = len(split_result) - 1
        result = []

        while current_split < last_split:
            company_prefix = split_result[current_split]
            if company_prefix.split(maxsplit=1)[0].lower() in COMPANY_SUFFIXES:
                current_split += 1
                company_prefix = ''.join([company_prefix, split_result[current_split].removesuffix('.')])
                if current_split == last_split:
                    result.append(company_prefix)
                    return result

            current_split += 1
            suffix_part = split_result[current_split].split(maxsplit=1)[0].removesuffix('.')
            result.append(''.join([company_prefix, suffix_part]))
            current_split += 1

        if current_split == last_split:
            result.append(split_result[-1].removesuffix('.'))

        return result

    test_cases = [
        "Adient Germany",
        "Adient Germany Inc",
        "Apple Inc and Google LLC",
        "BMW Germany GmbH",
        "Microsoft Corporation and Oracle Inc",
    ]

    for test_case in test_cases:
        try:
            result = _split_compound_client_name(test_case)
            print(f"Input: '{test_case}' → Output: {result}")
        except Exception as e:
            print(f"Input: '{test_case}' → Error: {e}")


def test_fallback_search_logic():
    """Test the fallback search logic that splits by first word."""
    print("=== Testing Fallback Search Logic ===")
    
    test_cases = [
        "Adient Germany",
        "BMW Germany",
        "Siemens Australia",
        "Microsoft Corporation",
        "Single",
    ]
    
    for client_name in test_cases:
        print(f"Testing: '{client_name}'")
        
        # Simulate the fallback logic from message_processor.py lines 669-676
        words = client_name.split()
        if len(words) > 1:
            first_word = words[0]
            print(f"  Would search for first word: '{first_word}'")
            print(f"  Original name preserved: '{client_name}'")
        else:
            print(f"  Single word, no fallback needed")
        print()


def test_data_merger():
    """Test the ExtractedDataMerger client name processing (simplified version)."""
    print("=== Testing ExtractedDataMerger ===")

    # Simplified version of the merger logic
    CLIENT_NAME_PATTERN = re.compile(
        f'({"|".join([rf"{suffix}.?$" for suffix in COMPANY_SUFFIXES])})', flags=re.IGNORECASE
    )

    print(f"CLIENT_NAME_PATTERN: {CLIENT_NAME_PATTERN.pattern}")

    # Test the pattern matching
    test_words = ["Germany", "Inc", "LLC", "Corp", "Ltd", "Limited", "Adient", "BMW"]
    print("\n=== Pattern Matching Test ===")
    for word in test_words:
        match = CLIENT_NAME_PATTERN.match(word)
        print(f"'{word}' matches pattern: {bool(match)} {f'(matched: {match.group()})' if match else ''}")

    def _merge_client_names(client_name: str, all_client_names):
        # Split client_name by comma and strip whitespace
        if client_name:
            client_names = [stripped for item in client_name.split(',') if (stripped := item.strip())]
        else:
            client_names = []

        # Initialize all_client_names if needed
        all_client_names = all_client_names or []

        existing_lower = {item.lower() for item in all_client_names}
        for client_name in client_names:
            print(f"  Processing: '{client_name}'")
            print(f"  Pattern match: {bool(CLIENT_NAME_PATTERN.match(client_name))}")
            print(f"  all_client_names: {all_client_names}")

            # Check if the client name is a company suffix and append it to the last client name
            if all_client_names and CLIENT_NAME_PATTERN.match(client_name):
                print(f"  → Treating '{client_name}' as suffix, appending to '{all_client_names[-1]}'")
                existing_lower.remove(all_client_names[-1].lower())
                all_client_names[-1] = ', '.join([all_client_names[-1], client_name])
                existing_lower.add(all_client_names[-1].lower())
            elif client_name.lower() not in existing_lower:
                print(f"  → Adding '{client_name}' as new client")
                all_client_names.append(client_name)
                existing_lower.add(client_name.lower())
            else:
                print(f"  → '{client_name}' already exists, skipping")

        return all_client_names or None

    print("\n=== Merge Client Names Test ===")
    test_cases = [
        "Adient Germany",
        "BMW Germany, Mercedes Germany",
        "Apple Inc, Google LLC",
    ]

    for test_case in test_cases:
        print(f"\nTesting: '{test_case}'")
        try:
            result = _merge_client_names(test_case, None)
            print(f"Final result: {result}")
        except Exception as e:
            print(f"Error: {e}")

    # Test the specific scenario that might cause the bug
    print("\n=== Testing Specific Bug Scenario ===")
    print("Scenario: What if 'Adient' is processed first, then 'Germany' is processed as a separate item?")

    # Simulate processing "Adient" first, then "Germany"
    result = _merge_client_names("Adient", None)
    print(f"After processing 'Adient': {result}")

    result = _merge_client_names("Germany", result)
    print(f"After processing 'Germany': {result}")

    # Test with comma-separated values that might be split incorrectly
    print("\n=== Testing Edge Cases ===")
    edge_cases = [
        "Adient, Germany",  # What if LLM returns this?
        "Germany",  # What if LLM returns just "Germany"?
        "Adient Germany, Inc",  # What if there's a suffix after?
    ]

    for case in edge_cases:
        print(f"\nTesting edge case: '{case}'")
        result = _merge_client_names(case, None)
        print(f"Result: {result}")


def main():
    """Run all tests to identify the truncation issue."""
    print("🔍 Investigating 'Adient Germany' → 'Germany' truncation bug\n")
    
    test_company_suffix_pattern()
    test_client_name_splitting()
    test_fallback_search_logic()
    test_data_merger()
    
    print("✅ Test completed. Check output above for potential issues.")


if __name__ == "__main__":
    main()
