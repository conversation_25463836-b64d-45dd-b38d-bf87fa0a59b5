# Confirmed Fields Business Logic - Document Processing

## Overview

This document describes the business logic for handling confirmed field changes in document processing within the AI assistant application. The system implements a sophisticated field replacement validation system that balances data integrity with user flexibility.

## Core Principles

### 1. Write Protection Architecture
The system maintains a **prohibited fields configuration** that defines which fields are protected from direct replacement:
- `CLIENT_NAME` - Always prohibited
- `LDMF_COUNTRY` - Always prohibited
- `OUTCOMES` - Prohibited by default, but conditionally allowed
- `OBJECTIVE_AND_SCOPE` - Prohibited by default, but conditionally allowed
- `DATE_INTERVALS` - Not prohibited, allows updates with validation

### 2. Conditional Validation Logic
Instead of blanket prohibitions, the system implements **conditional validation** that allows updates based on business context:
- **Separation of Concerns**: Write protection (prohibited fields) vs. business logic (conditional validation)
- **Backward Compatibility**: All existing functionality preserved
- **User Experience**: Better guidance through confirmation flows

## Field Update Rules

### Always Allowed Updates
- **Objectives & Outcomes**: Always allow updates/overwrites when business logic permits
- **Date Intervals**: Allow updates with proper validation and confirmation when needed
- **Other non-confirmed fields**: Continue allowing extending values in extracted data table

### Always Prohibited Updates
- **Client Name**: Never allow updates once confirmed
- **LDMF Country**: Never allow updates once confirmed

### Conditional Updates
- **Objectives & Outcomes**: Allowed when conversation state permits and validation passes
- **Date Intervals**: Allowed with unambiguous date confirmation when dates differ

## Date Confirmation Logic

### When Date Confirmation is Triggered
Date confirmation should be triggered when **ALL** of the following conditions are met:
1. User has already confirmed date intervals (existing confirmed dates)
2. New dates from document are unambiguous (day > 12 to avoid month/day confusion)
3. New dates are different from confirmed dates

### When Date Confirmation is NOT Triggered
Date confirmation should **NOT** be triggered when:
1. No confirmed dates exist yet (normal flow continues)
2. New dates are ambiguous (day ≤ 12, could be month/day confusion)
3. New dates match confirmed dates (no change needed)

### Date Confirmation Flow
When triggered, the system should:
1. Set conversation state to `COLLECTING_DATES`
2. Provide new dates as options for user confirmation
3. Allow user to choose between existing and new dates
4. Send `DateConfirmationNeeded` event with appropriate processing status

## Conversation State Handling

### DATA_COMPLETE State
When conversation state is `DATA_COMPLETE`:
- Use original logic for confirmed fields change prohibited messages
- Provide "Create My Qual" suggested prompts
- No further field extraction should occur

### Non-DATA_COMPLETE States
When conversation state is **not** `DATA_COMPLETE`:
- Use confirmation flow for field updates
- Integrate with `ProactiveChatService` for appropriate user guidance
- Provide field-specific options and confirmation flows
- Allow conditional updates based on field type and validation rules

## Error Handling

### ConfirmedDataReplacementError
When this error is detected:
1. System generates `CONFIRMED_FIELDS_CHANGE_PROHIBITED` system reply
2. Provides contextual guidance based on conversation state
3. Offers appropriate next steps or confirmation options
4. Maintains user experience through guided flows

### Document Processing Errors
- Handle corrupted documents with appropriate error messages
- Maintain processing state consistency
- Provide clear user feedback on processing issues

## Implementation Architecture

### Key Components
1. **Prohibited Fields Configuration** (`constants/extracted_data.py`)
   - Maintains write protection architecture
   - Defines which fields are protected by default

2. **Conditional Validation Logic** (`validators/extracted_data.py`)
   - Implements business logic for field updates
   - Allows conditional updates based on context

3. **Date Confirmation Infrastructure** (`durable_functions/`)
   - Handles unambiguous date detection and confirmation
   - Manages date confirmation workflow

4. **Message Handler Logic** (`services/message_handlers/`)
   - Provides user guidance and confirmation flows
   - Integrates with proactive chat services

### Processing Flow
1. Document processing extracts new field data
2. Validation logic checks for confirmed field replacements
3. Conditional validation allows or blocks updates based on business rules
4. Date confirmation logic triggers when appropriate
5. User receives appropriate guidance and confirmation options
6. System maintains data integrity while providing flexibility

## Examples

### Scenario 1: First Document Processing
- **Input**: New document with all 5 fields
- **Result**: All fields processed normally, some may be confirmed
- **State**: Conversation progresses based on field completeness

### Scenario 2: Second Document with Allowed Updates
- **Input**: Second document with different objectives and outcomes
- **Validation**: Conditional logic allows updates for these fields
- **Result**: Fields updated successfully, no error thrown
- **User Experience**: Smooth processing without interruption

### Scenario 3: Date Interval Confirmation
- **Input**: Second document with unambiguous dates (e.g., 2024-02-15)
- **Validation**: Dates differ from confirmed dates and are unambiguous
- **Result**: Date confirmation flow triggered
- **User Experience**: Presented with date options to choose from

### Scenario 4: Prohibited Field Update Attempt
- **Input**: Attempt to update client name or country
- **Validation**: Always blocked by prohibited fields configuration
- **Result**: `CONFIRMED_FIELDS_CHANGE_PROHIBITED` error
- **User Experience**: Clear error message with guidance

## Testing Strategy

The business logic is validated through comprehensive test coverage:
- **Configuration Tests**: Verify prohibited fields setup
- **Validation Tests**: Test conditional logic for each field type
- **Integration Tests**: End-to-end document processing scenarios
- **Message Handler Tests**: Confirmation flow behavior
- **Date Logic Tests**: Unambiguous date detection and confirmation

## Backward Compatibility

The implementation maintains full backward compatibility:
- Existing prohibited fields behavior preserved
- All existing tests continue to pass
- No breaking changes to existing APIs
- Enhanced user experience without disrupting current workflows
